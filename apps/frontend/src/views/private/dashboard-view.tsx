import { PlanningWithProvider, type PlanningLabels } from '@/components/layout/planning';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator.tsx';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Maximize2, Minimize2 } from 'lucide-react';
import { useState } from 'react';
import { useTourProgressQuery } from '@/lib/hooks/queries/tour-progress-queries';

// Types TypeScript pour les statistiques de livraison
interface DeliveryStats {
  name: string;
  value: number;
  color: string;
}

interface TourStats {
  id: string;
  name: string;
  progress: number;
  color: string;
}

export function DashboardView() {
  const [isFullScreen, setIsFullScreen] = useState(false);

  // Labels personnalisés pour le contexte tournées/chauffeurs
  const customLabels: PlanningLabels = {
    title: 'Planning',
    userLabel: 'Chauffeur',
    userLabelPlural: 'Chauffeurs',
    resourceLabel: 'Tournée',
    resourceLabelPlural: 'Tournées',
    assignUserLabel: 'Assigner un chauffeur',
    selectUserLabel: 'Sélectionner un chauffeur',
    removeAssignmentLabel: "Supprimer l'assignation",
    startDateLabel: 'Date de début',
    endDateLabel: 'Date de fin',
    todayLabel: "Aujourd'hui",
    holidayLabel: 'Férié',
    deleteResourceLabel: 'Supprimer la tournée',
    dragDropHintLabel: 'Chauffeurs disponibles (glisser-déposer)',
  };

  const { data: tourProgress } = useTourProgressQuery(new Date());

  const tourStats: TourStats[] = (tourProgress || []).map((tour) => ({
    id: tour.tourIdentifier.originalNumber,
    name: `Tournée ${tour.tourIdentifier.number}`,
    progress: tour.progress,
    color: '#10B981',
  }));

  const getDeliveryStatsForTour = (tourId: string): DeliveryStats[] => [
    { name: 'Partielles', value: 30, color: '#2B7FFF' },
    { name: 'En cours', value: 40, color: '#10B981' },
    { name: 'Terminées', value: 20, color: '#F59E0B' },
    { name: 'Annulées', value: 10, color: '#EF4444' },
  ];

  const TourTooltipContent = ({
    tourName,
    deliveryStats,
  }: {
    tourName: string;
    deliveryStats: DeliveryStats[];
  }) => (
    <div className="space-y-3 min-w-[200px]">
      <div className="text-sm font-semibold text-gray-900 border-b border-gray-200 pb-2">
        {tourName}
      </div>
      <div className="space-y-2">
        {deliveryStats.map((stat, index) => (
          <div key={index} className="flex items-center gap-3 text-sm">
            <div
              className="w-3 h-3 rounded-full flex-shrink-0"
              style={{ backgroundColor: stat.color }}
            />
            <span className="flex grow justify-between text-gray-700">
              <span>{stat.name}</span>
              <span className="font-medium">{stat.value}%</span>
            </span>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold">Tableau de bord</h1>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsFullScreen(!isFullScreen)}
          className="flex items-center gap-2"
        >
          {isFullScreen ? (
            <>
              <Minimize2 className="h-4 w-4" />
              Vue normale
            </>
          ) : (
            <>
              <Maximize2 className="h-4 w-4" />
              Plein écran
            </>
          )}
        </Button>
      </div>
      <Separator className="mb-6" />
      <div className={`grid gap-6 ${isFullScreen ? 'grid-cols-1' : 'grid-cols-1 xl:grid-cols-12'}`}>
        <div className={isFullScreen ? 'col-span-1' : 'col-span-1 xl:col-span-8'}>
          <PlanningWithProvider labels={customLabels} fullScreen={isFullScreen} />
        </div>

        {!isFullScreen && (
          <div className="col-span-1 xl:col-span-4 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  Progression des tournées du jour
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  {tourStats.map((tour) => (
                    <Tooltip key={tour.id}>
                      <TooltipTrigger asChild>
                        <div className="cursor-pointer hover:bg-gray-50 p-2 rounded-md transition-colors">
                          <div className="flex justify-between text-sm mb-1">
                            <span className="font-bold">{tour.name}</span>
                            <span>{tour.progress}%</span>
                          </div>
                          <Progress
                            value={tour.progress}
                            className="h-2"
                            style={
                              {
                                '--progress-background': tour.color,
                              } as React.CSSProperties
                            }
                          />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent
                        className="bg-white border-2 border-gray-300 shadow-2xl p-4"
                        sideOffset={8}
                      >
                        <TourTooltipContent
                          tourName={tour.name}
                          deliveryStats={getDeliveryStatsForTour(tour.id)}
                        />
                      </TooltipContent>
                    </Tooltip>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {isFullScreen && (
        <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">
                Progression des tournées du jour
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                {tourStats.map((tour) => (
                  <Tooltip key={tour.id}>
                    <TooltipTrigger asChild>
                      <div className="cursor-pointer hover:bg-gray-50 p-2 rounded-md transition-colors">
                        <div className="flex justify-between text-sm mb-1">
                          <span className="font-bold">{tour.name}</span>
                          <span>{tour.progress}%</span>
                        </div>
                        <Progress
                          value={tour.progress}
                          className="h-2"
                          style={
                            {
                              '--progress-background': tour.color,
                            } as React.CSSProperties
                          }
                        />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent
                      className="bg-white border-2 border-gray-300 shadow-2xl p-4"
                      sideOffset={8}
                    >
                      <TourTooltipContent
                        tourName={tour.name}
                        deliveryStats={getDeliveryStatsForTour(tour.id)}
                      />
                    </TooltipContent>
                  </Tooltip>
                ))}
              </div>
            </CardContent>
          </Card>
          {/*
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Autres statistiques</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Espace disponible pour d'autres widgets
              </p>
            </CardContent>
          </Card>*/}
        </div>
      )}
    </div>
  );
}
