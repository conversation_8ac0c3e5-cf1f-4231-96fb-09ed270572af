import type { ITourIdentifier } from '@/interfaces/entity/i-tour-identifier';
import { addMonths, eachDayOfInterval, endOfMonth, startOfMonth, subMonths } from 'date-fns';
import React, { useCallback, useMemo, useState } from 'react';
import { DEFAULT_LABELS } from './constants';
import { usePlanningContext } from './context/PlanningContext';
import { useAssignments } from './hooks/useAssignments';
import { useDeliverers } from './hooks/useDeliverers';
import { usePlanningData } from './hooks/usePlanningData';
import type { PlanningDraggedUser, PlanningLabels, PlanningSelectedCell } from './types';

// Import des composants
import { ITourAssignmentEntity } from '../../../interfaces/entity/i-tour-assignment-entity';
import { AssignmentModal } from './components/AssignmentModal';
import { DraggableUsersList } from './components/DraggableUsersList';
import { Header } from './components/Header';
import { Legend } from './components/Legend';
import { ResourceList } from './components/ResourceList';
import { Timeline } from './components/Timeline';

export interface PlanningProps {
  labels?: PlanningLabels;
  locale?: 'fr' | 'en';
  fullScreen?: boolean;
}

export const Planning: React.FC<PlanningProps> = ({
  labels: customLabels = {},
  fullScreen = false,
}) => {
  const { deliverers, isLoading: usersLoading } = useDeliverers();

  // Get holidays and tour management functions from context
  const { holidays, isLoadingHolidays } = usePlanningContext();

  const stableCurrentMonth = useMemo(() => new Date(), []);

  const {
    currentMonth,
    planningData,
    navigateToMonth,
    isLoading: planningLoading,
  } = usePlanningData(stableCurrentMonth);

  // Fusion des labels personnalisés avec les labels par défaut
  const labels = { ...DEFAULT_LABELS, ...customLabels };

  // États
  const [selectedCell, setSelectedCell] = useState<PlanningSelectedCell | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [draggedUser, setDraggedUser] = useState<PlanningDraggedUser | null>(null);

  // Hook pour la gestion des assignations
  const { backendAssignments, getAssignments, assignUserToPeriod, moveAssignment } = useAssignments(
    {
      currentMonth,
      planningData,
    },
  );

  // Calcul des jours du mois
  const daysInMonth = useMemo(() => {
    const start = startOfMonth(currentMonth);
    const end = endOfMonth(currentMonth);
    return eachDayOfInterval({ start, end });
  }, [currentMonth]);

  // Permettre les assignations sur toutes les dates
  const isPastDate = useCallback((date: Date): boolean => {
    return false;
  }, []);

  // Navigation entre les mois
  const handleNavigateMonth = useCallback(
    (direction: 'prev' | 'next') => {
      const newMonth =
        direction === 'prev' ? subMonths(currentMonth, 1) : addMonths(currentMonth, 1);
      navigateToMonth(newMonth);
    },
    [currentMonth, navigateToMonth],
  );

  // Gestion du clic sur une cellule vide (mode création)
  const handleCellClick = useCallback((tourIdentifier: ITourIdentifier, date: Date) => {
    setSelectedCell({ tourIdentifier, date });
    setModalOpen(true);
  }, []);

  // Gestion du clic sur une assignation (mode édition)
  const handleAssignmentClick = useCallback(
    (assignment: ITourAssignmentEntity, tourIdentifier: ITourIdentifier, date: Date) => {
      setSelectedCell({ tourIdentifier, date, existingAssignment: assignment });
      setModalOpen(true);
    },
    [],
  );

  // Gestion de la fermeture de la modal
  const handleModalClose = useCallback(() => {
    setModalOpen(false);
    setSelectedCell(null);
  }, []);

  // Drag & Drop handlers
  const handleDragStart = useCallback(
    (
      e: React.DragEvent,
      userId: string,
      sourceCell?: { tourIdentifier: ITourIdentifier; date: Date },
    ) => {
      setDraggedUser({ userId, sourceCell });
      e.dataTransfer.effectAllowed = 'move';
    },
    [],
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent, tourIdentifier: ITourIdentifier, date: Date) => {
      e.preventDefault();

      if (!draggedUser) return;

      if (draggedUser.sourceCell) {
        moveAssignment(
          draggedUser.userId,
          draggedUser.sourceCell.tourIdentifier,
          draggedUser.sourceCell.date,
          tourIdentifier,
          date,
        );
      } else {
        // Depuis la liste des utilisateurs
        assignUserToPeriod(draggedUser.userId, tourIdentifier, date, date);
      }

      setDraggedUser(null);
    },
    [draggedUser, moveAssignment, assignUserToPeriod],
  );

  // Obtenir les assignations avec contexte
  const getAssignmentsWithContext = useCallback(
    (tourIdentifier: ITourIdentifier, date: Date) => {
      return getAssignments(tourIdentifier, date);
    },
    [getAssignments],
  );

  // Obtenir l'assignation existante depuis la sélection ou null pour mode création
  const existingAssignment = selectedCell?.existingAssignment;

  if (usersLoading || planningLoading || isLoadingHolidays) {
    return (
      <div
        className={`relative w-full bg-white border rounded-lg shadow-lg overflow-hidden ${fullScreen ? 'h-[600px]' : 'h-[500px]'}`}
      >
        {/* Header skeleton */}
        <div className="flex items-center justify-between p-4 border-b bg-gray-50">
          <div className="flex items-center space-x-4">
            <div className="w-5 h-5 bg-gray-200 rounded animate-pulse"></div>
            <div className="w-48 h-6 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-gray-200 rounded-lg animate-pulse"></div>
            <div className="w-10 h-10 bg-gray-200 rounded-lg animate-pulse"></div>
          </div>
        </div>

        <div className="flex h-full">
          {/* Sidebar skeleton */}
          <div className="w-48 bg-gray-50 border-r">
            <div className="h-16 border-b bg-gray-100"></div>
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="h-16 flex items-center px-4 border-b border-gray-200">
                <div className="flex items-center space-x-3 w-full">
                  <div className="w-3 h-3 bg-gray-300 rounded-full animate-pulse"></div>
                  <div className="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>

          {/* Timeline skeleton */}
          <div className="flex-1 overflow-hidden">
            {/* Calendar header skeleton */}
            <div className="h-16 flex border-b bg-gray-100">
              {Array.from({ length: 7 }).map((_, index) => (
                <div key={index} className="w-32 border-r border-gray-200 p-2">
                  <div className="w-20 h-4 bg-gray-200 rounded animate-pulse mx-auto"></div>
                </div>
              ))}
            </div>

            {/* Calendar rows skeleton */}
            {Array.from({ length: 5 }).map((_, rowIndex) => (
              <div key={rowIndex} className="h-16 flex border-b border-gray-200">
                {Array.from({ length: 7 }).map((_, colIndex) => (
                  <div key={colIndex} className="w-32 border-r border-gray-100 p-1">
                    <div className="h-full flex flex-col space-y-1">
                      <div className="h-7 bg-gray-100 rounded animate-pulse"></div>
                      <div className="h-7 bg-gray-100 rounded animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>

        {/* Loading overlay with spinner */}
        <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center">
          <div className="text-center">
            <div className="relative">
              <div className="w-12 h-12 border-4 border-blue-100 border-t-blue-500 rounded-full animate-spin mx-auto"></div>
              <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-blue-300 rounded-full animate-ping mx-auto"></div>
            </div>
            <p className="mt-4 text-gray-600 font-medium">Chargement du planning...</p>
            <p className="mt-1 text-sm text-gray-400">
              {usersLoading && 'Récupération des utilisateurs...'}
              {planningLoading && 'Récupération des tournées...'}
              {isLoadingHolidays && 'Chargement des jours fériés...'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full bg-white border rounded-lg shadow-lg flex flex-col overflow-hidden`}>
      <Header
        currentMonth={currentMonth}
        onNavigateMonth={handleNavigateMonth}
        title={labels.title}
      />

      <div className="flex flex-1 min-h-0 overflow-y-auto max-h-[600px]">
        <ResourceList
          planningData={planningData}
          deliverers={deliverers}
          daysInMonth={daysInMonth}
          getAssignments={getAssignmentsWithContext}
          backendAssignments={backendAssignments}
        />

        <Timeline
          planningData={planningData}
          deliverers={deliverers}
          daysInMonth={daysInMonth}
          holidays={holidays}
          getAssignments={getAssignmentsWithContext}
          backendAssignments={backendAssignments}
          onCellClick={handleCellClick}
          onAssignmentClick={handleAssignmentClick}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          labels={{
            todayLabel: labels.todayLabel,
            holidayLabel: labels.holidayLabel,
          }}
        />
      </div>

      {/* Section du bas avec background uniforme */}
      <div className="border-t bg-gray-50">
        <Legend
          labels={{
            userLabel: labels.userLabel,
            todayLabel: labels.todayLabel,
          }}
        />

        <DraggableUsersList
          users={deliverers}
          onDragStart={handleDragStart}
          label={labels.dragDropHintLabel}
        />
      </div>

      {selectedCell && (
        <AssignmentModal
          open={modalOpen}
          onClose={handleModalClose}
          tourIdentifier={selectedCell.tourIdentifier}
          tourDisplayName={
            planningData.find(
              (t) =>
                t.tourIdentifier.number === selectedCell.tourIdentifier.number &&
                t.tourIdentifier.type === selectedCell.tourIdentifier.type,
            )?.displayName || `Tournée ${selectedCell.tourIdentifier.number}`
          }
          clickedDate={selectedCell.date}
          existingAssignment={existingAssignment}
          deliverers={deliverers}
        />
      )}
    </div>
  );
};
