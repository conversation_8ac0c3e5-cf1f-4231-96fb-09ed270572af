// planning/components/Header.tsx
import React from 'react';
import { ChevronLeft, ChevronRight, Calendar } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

interface HeaderProps {
  currentMonth: Date;
  onNavigateMonth: (direction: 'prev' | 'next') => void;
  title: string;
}

export const Header: React.FC<HeaderProps> = ({ currentMonth, onNavigateMonth, title }) => {
  return (
    <div className="flex items-center justify-between p-4 border-b bg-gray-50">
      <div className="flex items-center space-x-4">
        <Calendar className="text-gray-600" size={20} />
        <h2 className="text-lg font-semibold text-gray-800">
          {title} - {format(currentMonth, 'MMMM yyyy', { locale: fr })}
        </h2>
      </div>
      <div className="flex items-center space-x-2">
        <button
          onClick={() => onNavigateMonth('prev')}
          className="p-2 rounded-lg border hover:bg-gray-100 transition-colors"
        >
          <ChevronLeft size={16} />
        </button>
        <button
          onClick={() => onNavigateMonth('next')}
          className="p-2 rounded-lg border hover:bg-gray-100 transition-colors"
        >
          <ChevronRight size={16} />
        </button>
      </div>
    </div>
  );
};
