import Navbar from '@/components/layout/header/navbar.tsx';
import UserDropdownMenu from '@/components/layout/header/user-dropdown-menu.tsx';
import Logo from '@/components/layout/logo.tsx';

export default function Header() {
  return (
    <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 justify-between relative ">
      <div className="flex items-center gap-2">
        <Logo />
      </div>
      <div className="flex items-center gap-2">
        <Navbar />
        <UserDropdownMenu />
      </div>
    </header>
  );
}
