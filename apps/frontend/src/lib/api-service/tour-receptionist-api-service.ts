import type { ITourEntity } from '../../interfaces/entity/i-tour-entity';
import { apiClient, type ApiClient } from '../api-client/api-client';
import type { IControlTourEquipmentDto } from '../dto/control-tour-equipment.dto';

export class TourReceptionistApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async getTodayTours(): Promise<ITourEntity[]> {
    const response = await this.apiClient.get('/api/receptionist/tours/today');
    return response.data;
  }

  async controlTourEquipment(tourId: string, dto: IControlTourEquipmentDto): Promise<ITourEntity> {
    const response = await this.apiClient.post(`/api/receptionist/tours/${tourId}/control-equipment`, dto);
    return response.data;
  }
}

export const tourReceptionistApiService = new TourReceptionistApiService(apiClient);
