import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { DateTime } from 'luxon';
import { Between, In, Not, IsNull } from 'typeorm';
import { LogisticsEquipmentCount } from '../../domain/entity/logistics-equipment-count';
import { DeliveryStatus } from '../../domain/entity/stop-completion.entity';
import { TourIdentifier } from '../../domain/entity/tour-identifier';
import { TourEntity } from '../../domain/entity/tour.entity';
import { UserEntity } from '../../domain/entity/user.entity';
import { TourStatus } from '../../domain/enum/tour.enums';
import { TourAssignmentRepository } from '../../infrastructure/repository/tour-assignment.repository';
import { TourRepository } from '../../infrastructure/repository/tour.repository';
import { PaginationMetaDto, PaginationParamsDto } from '../dto/pagination.dto';
import { TourIncidentDto } from '../dto/tour/tour-incident.dto';
import { TourProgressDto } from '../dto/tour/tour-progress.dto';
import { PaginationAdapter } from '../pagination/pagination-adapter';

@Injectable()
export class TourService {
  private readonly paginationAdapter: PaginationAdapter<TourEntity>;

  constructor(
    private readonly tourRepository: TourRepository,
    private readonly tourAssignmentRepository: TourAssignmentRepository,
  ) {
    this.paginationAdapter = new PaginationAdapter(tourRepository, TourEntity);
  }

  async getPaginatedToursByDate(
    date: string,
    pagination: PaginationParamsDto,
    filters: Record<string, string>,
  ): Promise<{ items: TourEntity[]; meta: PaginationMetaDto }> {
    return this.paginationAdapter.paginate(
      pagination,
      {
        where: {
          deliveryDate: date,
        },
        relations: {
          stops: {
            completion: true,
          },
        },
      },
      filters,
    );
  }

  async getPaginatedToursByDateRange(
    startDate: string,
    endDate: string,
    pagination: PaginationParamsDto,
  ): Promise<{ items: TourEntity[]; meta: PaginationMetaDto }> {
    return this.paginationAdapter.paginate(pagination, {
      where: {
        deliveryDate: Between(startDate, endDate),
      },
    });
  }

  async getAllToursForUserAndDate(user: UserEntity, date: string): Promise<TourEntity[]> {
    const tourAssignments = await this.tourAssignmentRepository.findAssignmentsByUserAndDate(user.id, date);

    const assignedTourIdentifiers = tourAssignments.map((assignment) => assignment.tourIdentifier.originalNumber);

    if (!assignedTourIdentifiers?.length) {
      return [];
    }

    const tours = await this.tourRepository.find({
      where: {
        tourIdentifier: {
          originalNumber: In(assignedTourIdentifiers),
        },
        deliveryDate: date,
      },
      relations: {
        stops: {
          shipmentLines: true,
          deliveryNotes: {
            file: true,
          },
        },
      },
    });

    return tours;
  }

  async getToursProgressForUserAndDate(user: UserEntity, date: string): Promise<TourProgressDto[]> {
    const tours = await this.getAllToursForUserAndDate(user, date);

    return tours.map((tour) => {
      const totalStops = tour.stops?.length || 0;
      const completedStops =
        tour.stops?.filter((stop) => stop.completion?.deliveryStatus !== DeliveryStatus.PENDING).length || 0;

      const progress = totalStops > 0 ? Math.round((completedStops / totalStops) * 100) : 0;

      return {
        tourIdentifier: tour.tourIdentifier,
        progress,
        totalStops,
        completedStops,
      } as TourProgressDto;
    });
  }

  async getToursProgressForDate(date: string): Promise<TourProgressDto[]> {
    const tours = await this.tourRepository.find({
      where: {
        deliveryDate: date,
      },
      relations: {
        stops: {
          completion: true,
        },
      },
    });

    return tours.map((tour) => {
      const totalStops = tour.stops?.length || 0;
      const completedStops =
        tour.stops?.filter((stop) => stop.completion?.deliveryStatus !== DeliveryStatus.PENDING).length || 0;

      const progress = totalStops > 0 ? Math.round((completedStops / totalStops) * 100) : 0;

      return {
        tourIdentifier: tour.tourIdentifier,
        progress,
        totalStops,
        completedStops,
      } as TourProgressDto;
    });
  }

  async getDistinctTourIdentifiers(startDate?: string, endDate?: string): Promise<TourIdentifier[]> {
    return this.tourRepository.findDistinctTourIdentifiers(startDate, endDate);
  }

  async findTourById(id: string): Promise<TourEntity> {
    const tour = await this.tourRepository.findOne({
      where: { id },
      relations: {
        stops: {
          client: true,
          shipmentLines: true,
          deliveryNotes: {
            file: true,
          },
        },
        importBatch: true,
      },
    });

    if (!tour) {
      throw new NotFoundException(`Tour with ID ${id} not found`);
    }

    return tour;
  }

  async getTourIncidents(id: string): Promise<TourIncidentDto[]> {
    const tour = await this.tourRepository.findOne({
      where: { id },
      relations: {
        stops: {
          client: true,
          completion: {
            incidentType: true,
          },
        },
      },
    });

    if (!tour) {
      throw new NotFoundException(`Tour with ID ${id} not found`);
    }

    // Filter stops that have incidents and map to DTO
    const incidents: TourIncidentDto[] = tour.stops
      .filter((stop) => stop.completion?.incidentType != null)
      .map((stop) => ({
        stopId: stop.id,
        stopSequence: stop.sequenceInTour,
        clientName: stop.client?.name || stop.originalClientInfo?.name || 'Client inconnu',
        clientCode: stop.client?.code || stop.originalClientInfo?.code || 'Code inconnu',
        incidentType: stop.completion!.incidentType!,
        deliveryCompletionType: stop.completion!.deliveryCompletionType,
        comments: stop.completion!.comments,
        completedAt: stop.completion!.completedAt,
      }));

    // Sort by stop sequence
    return incidents.sort((a, b) => a.stopSequence - b.stopSequence);
  }

  async startTour(tourId: string, user: UserEntity): Promise<TourEntity> {
    const tour = await this.tourRepository.findOne({
      where: { id: tourId },
      relations: [],
    });

    if (!tour) {
      throw new NotFoundException(`Tour with ID ${tourId} not found`);
    }

    // Check if user has access to this tour
    const tourDate = DateTime.fromISO(tour.deliveryDate);
    const userAssignments = await this.tourAssignmentRepository.findAssignmentsByUserAndDate(
      user.id,
      tourDate.toISODate(),
    );

    const hasAccess = userAssignments.some(
      (assignment) => assignment.tourIdentifier.originalNumber === tour.tourIdentifier.originalNumber,
    );

    if (!hasAccess) {
      throw new ForbiddenException('You do not have access to this tour');
    }

    // Validate tour can be started
    if (tour.status !== TourStatus.Planned) {
      throw new BadRequestException(
        `Tour cannot be started. Current status is ${tour.status}, but only tours with status ${TourStatus.Planned} can be started`,
      );
    }

    // Start the tour
    tour.status = TourStatus.InProgress;

    return this.tourRepository.save(tour);
  }

  async computeAndUpdateTourEquipmentCount(tourId: string): Promise<void> {
    const tour = await this.tourRepository.findOne({
      where: { id: tourId },
      relations: { stops: true },
    });

    if (!tour) {
      throw new NotFoundException(`Tour with ID ${tourId} not found`);
    }

    const loaded = new LogisticsEquipmentCount();
    const preloaded = new LogisticsEquipmentCount();
    const returned = new LogisticsEquipmentCount();
    const unloaded = new LogisticsEquipmentCount();

    for (const stop of tour.stops) {
      if (stop.completion) {
        loaded.palletCount += stop.completion.loadedEquipmentCount?.palletCount || 0;
        loaded.rollCount += stop.completion.loadedEquipmentCount?.rollCount || 0;
        loaded.packageCount += stop.completion.loadedEquipmentCount?.packageCount || 0;

        preloaded.palletCount += stop.completion.preloadedEquipmentCount?.palletCount || 0;
        preloaded.rollCount += stop.completion.preloadedEquipmentCount?.rollCount || 0;
        preloaded.packageCount += stop.completion.preloadedEquipmentCount?.packageCount || 0;

        returned.palletCount += stop.completion.returnedEquipmentCount?.palletCount || 0;
        returned.rollCount += stop.completion.returnedEquipmentCount?.rollCount || 0;
        returned.packageCount += stop.completion.returnedEquipmentCount?.packageCount || 0;

        unloaded.palletCount += stop.completion.unloadedEquipmentCount?.palletCount || 0;
        unloaded.rollCount += stop.completion.unloadedEquipmentCount?.rollCount || 0;
        unloaded.packageCount += stop.completion.unloadedEquipmentCount?.packageCount || 0;
      }
    }

    tour.totalLoadedEquipmentCount = loaded;
    tour.totalPreloadedEquipmentCount = preloaded;
    tour.totalReturnedEquipmentCount = returned;
    tour.totalUnloadedEquipmentCount = unloaded;

    await this.tourRepository.save(tour);
  }

  async getToursWithEquipmentDiscrepancies(deliveryDate: string): Promise<TourEntity[]> {
    // Validation du format de date
    if (!/^\d{4}-\d{2}-\d{2}$/.test(deliveryDate)) {
      throw new BadRequestException('Invalid date format. Expected YYYY-MM-DD');
    }

    const tours = await this.tourRepository.find({
      where: {
        deliveryDate: deliveryDate,
        controlledEquipmentCount: Not(IsNull()),
        totalReturnedEquipmentCount: Not(IsNull()),
      },
    });

    return tours.filter((tour) => this.hasEquipmentDiscrepancy(tour));
  }

  private hasEquipmentDiscrepancy(tour: TourEntity): boolean {
    const controlled = tour.controlledEquipmentCount;
    const returned = tour.totalReturnedEquipmentCount;

    if (!controlled || !returned) {
      return false;
    }

    // Normalize undefined/null values to 0 for comparison
    const normalizeCount = (count: number | undefined | null): number => count ?? 0;

    return (
      normalizeCount(controlled.palletCount) !== normalizeCount(returned.palletCount) ||
      normalizeCount(controlled.rollCount) !== normalizeCount(returned.rollCount) ||
      normalizeCount(controlled.packageCount) !== normalizeCount(returned.packageCount)
    );
  }
}
