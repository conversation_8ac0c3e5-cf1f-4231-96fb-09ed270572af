import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { In } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { LogisticsEquipmentCount } from '../../domain/entity/logistics-equipment-count';
import { DeliveryStatus } from '../../domain/entity/stop-completion.entity';
import { StopEntity } from '../../domain/entity/stop.entity';
import { TourEntity } from '../../domain/entity/tour.entity';
import { LogisticsEquipmentOperation } from '../../domain/enum/logistics-equipment-operation.enum';
import { LogisticsEquipmentDetailsRepository } from '../../infrastructure/repository/logistics-equipment-details.repository';
import { LogisticsEquipmentTypeRepository } from '../../infrastructure/repository/logistics-equipment-type.repository';
import { StopRepository } from '../../infrastructure/repository/stop.repository';
import { TourRepository } from '../../infrastructure/repository/tour.repository';
import { LoadStopDto } from '../dto/stop/load-stop.dto';
import { LogisticsEquipmentDetailDto } from '../dto/stop/logistics-equipment-detail.dto';
import { TourService } from '../service/tour.service';

/**
 * Use case pour effectuer le préchargement d'un arrêt
 *
 * Permet au chauffeur de préparer et enregistrer l'équipement logistique
 * en amont de sa tournée, généralement la veille ou le matin même.
 * Cette étape de préparation facilite l'organisation logistique sans
 * déclencher le début officiel de la tournée.
 *
 * Fonctionnalités :
 * - Enregistrement du matériel préchargé (palettes, rolls, colis)
 * - Validation des types d'équipement autorisés
 * - Préparation logistique sans impact sur le statut de tournée
 * - Calcul des totaux d'équipement préchargé
 *
 * Règles métier :
 * - L'arrêt doit être en statut "En attente"
 * - Le statut de la tournée reste inchangé (Planifiée)
 * - Au moins un type d'équipement doit être spécifié
 * - Permet une préparation flexible avant le chargement définitif
 */
@Injectable()
export class PreloadStopUseCase {
  constructor(
    private readonly stopRepository: StopRepository,
    private readonly tourRepository: TourRepository,
    private readonly logisticsEquipmentDetailsRepository: LogisticsEquipmentDetailsRepository,
    private readonly logisticsEquipmentTypeRepository: LogisticsEquipmentTypeRepository,
    private readonly tourService: TourService,
  ) {}

  @Transactional()
  async execute(stopId: string, dto: LoadStopDto): Promise<StopEntity> {
    const { stop, tour } = await this.getAndValidateInputStop(stopId);

    if (dto.equipmentCount) {
      stop.completion.preloadedEquipmentCount = LogisticsEquipmentCount.fromDto(dto.equipmentCount);
    }

    if (dto.equipmentDetails && dto.equipmentDetails.length > 0) {
      await this.validateEquipmentDetails(dto.equipmentDetails);

      await this.updateStopPreloadedEquipmentCount(stop, dto.equipmentDetails);
    }

    const updatedStop = await this.stopRepository.save(stop);

    await this.tourService.computeAndUpdateTourEquipmentCount(tour.id);

    return updatedStop;
  }

  private async getAndValidateInputStop(stopId: string): Promise<{ stop: StopEntity; tour: TourEntity }> {
    const stop = await this.stopRepository.findOne({ where: { id: stopId } });

    if (!stop) {
      throw new NotFoundException(`Stop with ID ${stopId} not found`);
    }

    if (stop.completion.deliveryStatus !== DeliveryStatus.PENDING) {
      throw new BadRequestException(
        `Stop is not pending, it is already completed with status ${stop.completion.deliveryStatus}`,
      );
    }

    const tour = await this.tourRepository.findOne({
      where: { id: stop.tourId },
    });

    if (!tour) {
      throw new NotFoundException(`Tour with ID ${stop.tourId} not found`);
    }

    return { stop, tour };
  }

  private async validateEquipmentDetails(equipmentDetails: LogisticsEquipmentDetailDto[]): Promise<void> {
    const equipmentTypes = await this.logisticsEquipmentTypeRepository.find({
      where: {
        id: In(equipmentDetails.map((detail) => detail.logisticsEquipmentTypeId)),
      },
    });

    if (equipmentTypes.length !== equipmentDetails.length) {
      throw new NotFoundException(
        `Invalid equipment details, some equipment types are not found: ${equipmentDetails
          .map((detail) => detail.logisticsEquipmentTypeId)
          .join(', ')}`,
      );
    }
  }

  private async updateStopPreloadedEquipmentCount(
    stop: StopEntity,
    equipmentDetails: LogisticsEquipmentDetailDto[],
  ): Promise<void> {
    await this.logisticsEquipmentDetailsRepository.delete({
      stopId: stop.id,
      operation: LogisticsEquipmentOperation.PRELOADED,
    });

    const equipmentDetailsToSave = equipmentDetails.map((detail) => {
      return this.logisticsEquipmentDetailsRepository.create({
        stopId: stop.id,
        operation: LogisticsEquipmentOperation.PRELOADED,
        quantity: detail.quantity,
        logisticsEquipmentTypeId: detail.logisticsEquipmentTypeId,
      });
    });

    const savedEquipmentDetails = await this.logisticsEquipmentDetailsRepository.save(equipmentDetailsToSave);

    const updatedEquipmentDetails = await this.logisticsEquipmentDetailsRepository.find({
      where: { id: In(savedEquipmentDetails.map((detail) => detail.id)) },
      relations: {
        logisticsEquipmentType: true,
      },
    });

    stop.completion.preloadedEquipmentCount = LogisticsEquipmentCount.fromEquipmentDetails(updatedEquipmentDetails);
  }
}
