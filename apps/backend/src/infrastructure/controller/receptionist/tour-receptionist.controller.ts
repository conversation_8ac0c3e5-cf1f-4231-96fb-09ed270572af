import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DateTime } from 'luxon';
import { Roles } from 'nest-keycloak-connect';
import { QueryParamsDto } from '../../../application/dto/pagination.dto';
import { ControlTourEquipmentDto } from '../../../application/dto/tour/control-tour-equipment.dto';
import { TourService } from '../../../application/service/tour.service';
import { ControlTourEquipmentUseCase } from '../../../application/use-case/control-tour-equipment.use-case';
import { TourEntity } from '../../../domain/entity/tour.entity';
import { UserRole } from '../../../domain/enum/user-role.enum';

@ApiTags('receptionist/tours')
@Controller('receptionist/tours')
@UseInterceptors(ClassSerializerInterceptor)
@Roles({ roles: [`realm:${UserRole.Receptionist}`] })
export class TourReceptionistController {
  constructor(
    private readonly tourService: TourService,
    private readonly controlTourEquipmentUseCase: ControlTourEquipmentUseCase,
  ) {}

  @Get('today')
  @ApiOperation({
    summary: 'Get all tours for today - receptionist can see all tours',
  })
  @ApiResponse({
    status: 200,
    type: [TourEntity],
    description: 'List of all tours for today',
  })
  async getAllToursForToday(): Promise<TourEntity[]> {
    const today = DateTime.now().toISODate();

    const paginationParams = new QueryParamsDto();
    paginationParams.page = 1;
    paginationParams.limit = 1000; // Large limit to get all tours

    const result = await this.tourService.getPaginatedToursByDate(today, paginationParams, {});

    return result.items;
  }

  @Post(':id/control-equipment')
  @ApiOperation({
    summary: 'Control equipment for a tour',
    description: 'Allows receptionists to control and validate logistics equipment present in a tour',
  })
  @ApiResponse({
    status: 200,
    type: TourEntity,
    description: 'Tour with updated controlled equipment information',
  })
  async controlTourEquipment(
    @Param('id', ParseUUIDPipe) tourId: string,
    @Body() dto: ControlTourEquipmentDto,
  ): Promise<TourEntity> {
    return this.controlTourEquipmentUseCase.execute(tourId, dto);
  }
}
