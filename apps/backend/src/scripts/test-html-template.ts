import { writeFileSync } from 'fs';
import { HtmlRenderService } from '../infrastructure/service/html-render/html-render.service';
import { StopCompletionDocumentContext, MerchandiseItem } from '../infrastructure/service/html-render/contexts';

async function generateTestHtml() {
  const htmlRenderService = new HtmlRenderService();

  const context = new StopCompletionDocumentContext();

  context.shipmentNumber = '93.614425.10062';
  context.clientRef = '3202510062';
  context.clientRef3 = '18-943 / 1466611';

  context.transportCompanyName = 'Galliker Transports SA';
  context.transportCompanyAddressLines = ['Aclens', 'Le Bochet', '1123 Aclens'];
  context.transportCompanyPhone = '+41 21 867 11 80';
  context.transportCompanyEmail = '<EMAIL>';

  context.senderName = 'LRG Ice + Co';
  context.senderCompany = 'c/o LRG Logistics SA';
  context.senderAddressLines = ['Neufeldweg 1', '4913 Bannwil'];

  context.truckNumber = '10.027';

  context.recipientName = 'INSTITUT EQUESTRE NATIONAL';
  context.recipientLocation = 'AVENCHES';
  context.recipientPhone = '079 739 27 65';
  context.recipientContact = 'Simone König';
  context.recipientAddressLines = ['LES LONGS-PRES 1A', '1580 Avenches'];

  context.deliveryStatus = 'Livré';
  context.deliveryDate = '25.04.2025';
  context.totalPackages = 1;
  context.totalWeight = '118 kg';
  context.temperatureInfo = 'Marchandises réfrigérées 5°';
  context.showTemperatureInfo = true;

  context.deliveryHours = '12:00 - 17:00';
  context.showDeliveryHours = true;

  context.signatureName = 'Proyot';
  context.signatureDateTime = '25.04.2025 12:00:01';

  context.showQrCode = true;
  context.qrCodeText = 'Google Maps';

  context.arrivalTime = '13:28 heur';
  context.waitingTime = '0 min';
  context.departureTime = '13:35 heur';
  context.showTimingInfo = true;

  context.clientRemarks = '18-943/ 1466611';
  context.showClientRemarks = true;

  const merchandiseItems: MerchandiseItem[] = [
    { quantity: 1, unit: 'PALT', designation: 'Pallets', weight: 118 },
    {
      quantity: 2,
      unit: 'CARTON',
      designation: 'Cartons réfrigérés',
      weight: 25,
    },
    { quantity: 5, unit: 'SACS', designation: 'Sacs de légumes', weight: 30 },
  ];
  context.merchandiseItems = merchandiseItems;

  context.currentPage = 1;
  context.totalPages = 1;

  const fakeSignatureBuffer = Buffer.from(
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
    'base64',
  );
  context.setSignatureFromBuffer(fakeSignatureBuffer);

  const fakeQrCodeBuffer = Buffer.from(
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
    'base64',
  );
  context.setQrCodeFromBuffer(fakeQrCodeBuffer);

  try {
    const html = await htmlRenderService.readTemplateAndRender('stop-completion-document.template.html', context);

    writeFileSync('./test-output.html', html);
    console.log('HTML file generated: test-output.html');
    console.log('Open the file in your browser to view the rendered template');
  } catch (error) {
    console.error('Error generating HTML:', error);
  }
}

generateTestHtml();
