import { IonContent, IonModal, IonText } from '@ionic/react';
import { X } from 'lucide-react';
import { IPreloadStopDto } from '../../../../interfaces/dto/load-stop.dto';
import { IStopEntity } from '../../../../interfaces/entity/i-stop-entity';
import { ITourEntity } from '../../../../interfaces/entity/i-tour-entity';
import { LogisticsEquipmentKind } from '../../../../interfaces/enum/logistics-equipment-kind.enum';
import { TourStatus } from '../../../../interfaces/enum/tour.enums';
import {
  selectAreAllStopsValidated,
  selectPreloadStopDtos,
  updatePreloadStopDto,
} from '../../../../stores/receptionistPreloadSlice';
import { formatDate } from '../../../../utils/dateUtils';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';
import { EquipmentItem } from '../../../services/deliverer/EquipmentValidator';
import { EquipmentValidationSection } from '../../../services/deliverer/loading-control/EquipmentValidationSection';
import InlineButtons from '../../../ui/stylized/InlineButtons';

interface PreloadTourModalProps {
  tour: ITourEntity | null;
  isOpen: boolean;
  onClose: () => void;
  onValidate: (tour: ITourEntity) => void;
}

export function PreloadTourModal({ tour, isOpen, onClose, onValidate }: PreloadTourModalProps) {
  const dispatch = useAppDispatch();

  // Utiliser les sélecteurs Redux pour l'état de validation
  const preloadStopDtos = useAppSelector(selectPreloadStopDtos);
  const areAllStopsValidated = useAppSelector((state) =>
    tour ? selectAreAllStopsValidated(state, tour) : false,
  );

  const getValidationButtonText = () => {
    if (!tour) {
      return '';
    }

    if (tour.status === TourStatus.Completed) {
      return 'Valider le contrôle de retour';
    }

    return 'Valider le pré-chargement';
  };

  const handleValidation = () => {
    if (tour && areAllStopsValidated) {
      onValidate(tour);
    }
  };

  // Convertir IPreloadStopDto en EquipmentItem[] pour l'affichage
  const getEquipmentItemsForStop = (stopId: string): EquipmentItem[] => {
    const stopDto = preloadStopDtos[stopId];

    if (!stopDto) {
      return [];
    }

    const items: EquipmentItem[] = [];

    // Convertir equipmentCount en EquipmentItem si présent
    if (stopDto.equipmentCount) {
      if (stopDto.equipmentCount.palletCount) {
        items.push({
          type: LogisticsEquipmentKind.PALLET,
          receivedQuantity: stopDto.equipmentCount.palletCount,
          adjustedQuantity: stopDto.equipmentCount.palletCount,
          isValidated: true,
        });
      }

      if (stopDto.equipmentCount.rollCount) {
        items.push({
          type: LogisticsEquipmentKind.ROLL,
          receivedQuantity: stopDto.equipmentCount.rollCount,
          adjustedQuantity: stopDto.equipmentCount.rollCount,
          isValidated: true,
        });
      }

      if (stopDto.equipmentCount.packageCount) {
        items.push({
          type: LogisticsEquipmentKind.PACKAGE,
          receivedQuantity: stopDto.equipmentCount.packageCount,
          adjustedQuantity: stopDto.equipmentCount.packageCount,
          isValidated: true,
        });
      }
    }

    return items;
  };

  // Convertir EquipmentItem[] en IPreloadStopDto
  const convertEquipmentItemsToDto = (items: EquipmentItem[]): IPreloadStopDto => {
    const equipmentCount: any = {};

    items.forEach((item) => {
      if (item.adjustedQuantity > 0) {
        switch (item.type) {
          case LogisticsEquipmentKind.PALLET:
            equipmentCount.palletCount = item.adjustedQuantity;
            break;
          case LogisticsEquipmentKind.ROLL:
            equipmentCount.rollCount = item.adjustedQuantity;
            break;
          case LogisticsEquipmentKind.PACKAGE:
            equipmentCount.packageCount = item.adjustedQuantity;
            break;
        }
      }
    });

    return {
      equipmentCount: Object.keys(equipmentCount).length > 0 ? equipmentCount : undefined,
    };
  };

  const handleEquipmentChange = (stopId: string, newItems: EquipmentItem[]) => {
    const preloadStopDto = convertEquipmentItemsToDto(newItems);
    dispatch(updatePreloadStopDto({ stopId, preloadStopDto }));
  };

  const handleValidationChange = (_stopId: string, _isValid: boolean) => {
    // La validation est automatique basée sur les quantités
    // Pas besoin d'action spécifique ici
  };

  const handleToggleAllEquipments = (_stopId: string, _isChecked: boolean) => {
    // Pour l'instant, on ne fait rien de spécial
    // Cette fonction pourrait être utilisée pour valider/invalider tous les équipements
  };

  const renderStopCard = (stop: IStopEntity) => {
    const items = getEquipmentItemsForStop(stop.id);

    if (!tour) {
      return null;
    }

    return (
      <EquipmentValidationSection
        key={stop.id}
        stop={stop}
        equipmentItems={items}
        onEquipmentChange={(newItems) => handleEquipmentChange(stop.id, newItems)}
        onValidationChange={(isValid) => handleValidationChange(stop.id, isValid)}
        onToggleAllEquipments={(isChecked) => handleToggleAllEquipments(stop.id, isChecked)}
      />
    );
  };

  return (
    <IonModal isOpen={isOpen} onDidDismiss={onClose}>
      <div className="shadow-none border-none">
        <div className="flex justify-between items-center px-4 py-4 border-b border-gray-200 bg-gray-50 primary-color">
          <div className="flex flex-col gap-2">
            <div className="text-xl font-bold">
              {tour?.status === TourStatus.Completed
                ? 'Tournée finalisée'
                : 'Pré-chargement des agrès'}{' '}
              n°{tour?.tourIdentifier.originalNumber}
            </div>
            <div className="text-sm font-medium text-gray-900/80">
              {tour && formatDate(tour.deliveryDate)}
            </div>
          </div>
          <div className="cursor-pointer" onClick={onClose}>
            <X className="h-6 w-6" />
          </div>
        </div>
      </div>

      <IonContent className="ion-padding">
        {tour && (
          <div className="pt-2 pb-20">
            {/* Liste des cartes de livraisons */}
            <div className="space-y-4 mb-8">
              {tour.stops && tour.stops.length > 0 ? (
                tour.stops.map(renderStopCard)
              ) : (
                <div className="text-center py-8">
                  <IonText>
                    <p className="text-lg text-neutral-500">Aucune livraison dans cette tournée</p>
                  </IonText>
                </div>
              )}
            </div>
          </div>
        )}
      </IonContent>

      {/* Bouton de validation flottant dans la modal */}
      {tour && (
        <div className="fixed bottom-0 bg-white py-6 left-0 right-0 z-10 p-4">
          <div className="max-w-7xl mx-4">
            <InlineButtons
              buttons={[
                {
                  label: getValidationButtonText(),
                  onClick: handleValidation,
                  disabled: !areAllStopsValidated,
                  classNames: {
                    label: 'text-lg font-semibold',
                    button: areAllStopsValidated
                      ? 'primary-button py-6 px-6'
                      : 'primary-button-outline opacity-50 py-6 px-6',
                  },
                },
              ]}
            />
          </div>
        </div>
      )}
    </IonModal>
  );
}
