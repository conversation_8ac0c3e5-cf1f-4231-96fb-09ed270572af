import { IonContent, IonModal, IonText } from '@ionic/react';
import { X } from 'lucide-react';
import { useMemo } from 'react';
import { useTourValidationState } from '../../../../hooks/useTourValidationState';
import { IStopEntity } from '../../../../interfaces/entity/i-stop-entity';
import { ITourEntity } from '../../../../interfaces/entity/i-tour-entity';
import { TourStatus } from '../../../../interfaces/enum/tour.enums';
import { formatDate } from '../../../../utils/dateUtils';
import { EquipmentValidationSection } from '../../../services/deliverer/loading-control/EquipmentValidationSection';
import InlineButtons from '../../../ui/stylized/InlineButtons';

interface PreloadTourModalProps {
  tour: ITourEntity | null;
  isOpen: boolean;
  onClose: () => void;
  onValidate: (tour: ITourEntity) => void;
}

export function PreloadTourModal({ tour, isOpen, onClose, onValidate }: PreloadTourModalProps) {
  const tourValidation = useTourValidationState({ workflow: 'preload' });

  // Calculer si tous les stops sont validés pour la tournée sélectionnée
  const areAllStopsValidated = useMemo(() => {
    if (!tour) {
      return false;
    }

    return (
      tour.stops?.every((stop) => {
        const validation = tourValidation.stopValidationStates[stop.id];

        return validation;
      }) || false
    );
  }, [tour, tourValidation.stopValidationStates]);

  const getValidationButtonText = () => {
    if (!tour) {
      return '';
    }

    if (tour.status === TourStatus.Completed) {
      return 'Valider le contrôle de retour';
    }

    return 'Valider le pré-chargement';
  };

  const handleValidation = () => {
    if (tour && areAllStopsValidated) {
      onValidate(tour);
    }
  };

  const renderStopCard = (stop: IStopEntity) => {
    const items = tourValidation.stopEquipmentStates[stop.id] || [];

    if (!tour) {
      return null;
    }

    return (
      <EquipmentValidationSection
        key={stop.id}
        stop={stop}
        equipmentItems={items}
        onEquipmentChange={(newItems) =>
          tourValidation.handleEquipmentChange(stop.id, tour.id, newItems)
        }
        onValidationChange={(isValid) =>
          tourValidation.handleStopValidation(stop.id, tour.id, isValid)
        }
        onToggleAllEquipments={(isChecked) =>
          tourValidation.handleToggleAllEquipments(stop.id, tour.id, isChecked)
        }
      />
    );
  };

  return (
    <IonModal isOpen={isOpen} onDidDismiss={onClose}>
      <div className="shadow-none border-none">
        <div className="flex justify-between items-center px-4 py-4 border-b border-gray-200 bg-gray-50 primary-color">
          <div className="flex flex-col gap-2">
            <div className="text-xl font-bold">
              {tour?.status === TourStatus.Completed
                ? 'Tournée finalisée'
                : 'Pré-chargement des agrès'}{' '}
              n°{tour?.tourIdentifier.originalNumber}
            </div>
            <div className="text-sm font-medium text-gray-900/80">
              {tour && formatDate(tour.deliveryDate)}
            </div>
          </div>
          <div className="cursor-pointer" onClick={onClose}>
            <X className="h-6 w-6" />
          </div>
        </div>
      </div>

      <IonContent className="ion-padding">
        {tour && (
          <div className="pt-2 pb-20">
            {/* Liste des cartes de livraisons */}
            <div className="space-y-4 mb-8">
              {tour.stops && tour.stops.length > 0 ? (
                tour.stops.map(renderStopCard)
              ) : (
                <div className="text-center py-8">
                  <IonText>
                    <p className="text-lg text-neutral-500">Aucune livraison dans cette tournée</p>
                  </IonText>
                </div>
              )}
            </div>
          </div>
        )}
      </IonContent>

      {/* Bouton de validation flottant dans la modal */}
      {tour && (
        <div className="fixed bottom-0 bg-white py-6 left-0 right-0 z-10 p-4">
          <div className="max-w-7xl mx-4">
            <InlineButtons
              buttons={[
                {
                  label: getValidationButtonText(),
                  onClick: handleValidation,
                  disabled: !areAllStopsValidated,
                  classNames: {
                    label: 'text-lg font-semibold',
                    button: areAllStopsValidated
                      ? 'primary-button py-6 px-6'
                      : 'primary-button-outline opacity-50 py-6 px-6',
                  },
                },
              ]}
            />
          </div>
        </div>
      )}
    </IonModal>
  );
}
