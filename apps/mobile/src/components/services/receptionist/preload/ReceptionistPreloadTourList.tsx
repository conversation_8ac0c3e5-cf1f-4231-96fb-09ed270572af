import { useEffect, useState } from 'react';
import { setReceptionistPreloadTourList } from '../../../../stores/receptionistPreloadSlice';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';
import { Loading } from '../../../layout/Loading';

export function ReceptionistPreloadTourList() {
  const dispatch = useAppDispatch();
  const globalTourList = useAppSelector((state) => state.tour.tours);
  const isGlobalTourListLoaded = useAppSelector((state) => state.tour.isDataResolved);

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (isGlobalTourListLoaded) {
      dispatch(setReceptionistPreloadTourList(globalTourList));
      setIsLoading(false);
    }
  }, [dispatch, globalTourList, isGlobalTourListLoaded]);

  if (isLoading) {
    return <Loading />;
  }
}
