import {
  IonButton,
  IonLabel,
  IonSegment,
  IonSegmentButton,
  IonText,
  useIonToast,
} from '@ionic/react';
import { useEffect } from 'react';
import { tourAssignmentManagerService, tourReceptionistService } from '../../../../services';
import {
  clearSearchTerm,
  selectDepartureTours,
  selectFilteredTours,
  selectReturnTours,
  setCurrentSegment,
  setError,
  setLoading,
  setSearchTerm,
  setTours,
  TourWithDriver,
} from '../../../../stores/receptionistPreloadSlice';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';
import { Loading } from '../../../layout/Loading';
import { SearchInput } from '../../../ui/shared/SearchInput';
import { TourCard } from '../../../ui/shared/TourCard';

export function ReceptionistPreloadTourList() {
  const dispatch = useAppDispatch();
  const [presentToast] = useIonToast();

  // Sélecteurs Redux
  const { searchTerm, currentSegment, isLoading, error } = useAppSelector(
    (state) => state.receptionistPreload,
  );

  const departureTours = useAppSelector(selectDepartureTours);
  const returnTours = useAppSelector(selectReturnTours);
  const filteredTours = useAppSelector(selectFilteredTours);

  const currentUser = useAppSelector((state) => state.currentUser.user);

  // Charger les tournées au montage du composant
  useEffect(() => {
    loadToursAndAssignments();
  }, []);

  const loadToursAndAssignments = async () => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      // Récupérer toutes les tournées d'aujourd'hui ET toutes les assignations
      const [toursArray, assignmentsResponse] = await Promise.all([
        tourReceptionistService.getTodayTours(),
        tourAssignmentManagerService.getTourAssignments({
          limit: 5000,
        }),
      ]);

      // Créer une map des assignations pour un matching plus efficace
      const assignmentMap = new Map();
      assignmentsResponse.items.forEach((assignment) => {
        const key = assignment.tourIdentifier.originalNumber;

        if (!assignmentMap.has(key)) {
          assignmentMap.set(key, []);
        }
        assignmentMap.get(key).push(assignment);
      });

      // Joindre les tournées avec les informations des chauffeurs
      const toursWithDrivers: TourWithDriver[] = [];

      toursArray.forEach((tour) => {
        // Chercher l'assignation correspondante
        const assignments = assignmentMap.get(tour.tourIdentifier.originalNumber) || [];

        // Filtrer les assignations pour cette date de livraison spécifique
        const validAssignments = assignments.filter((assignment: any) => {
          const assignmentFromDate = assignment.fromDate;
          const assignmentToDate = assignment.toDate;
          const tourDeliveryDate = tour.deliveryDate;

          // Vérifier si la date de livraison de la tournée est dans la plage de l'assignation
          if (assignmentToDate) {
            return tourDeliveryDate >= assignmentFromDate && tourDeliveryDate <= assignmentToDate;
          } else {
            // Assignation ouverte (pas de date de fin)
            return tourDeliveryDate >= assignmentFromDate;
          }
        });

        if (validAssignments.length > 0) {
          // Prendre la première assignation valide
          const assignment = validAssignments[0];
          const driverFirstName = assignment?.user?.firstName || '';
          const driverLastName = assignment?.user?.lastName || '';
          const driverName = `${driverFirstName} ${driverLastName}`.trim() || 'Non assigné';

          toursWithDrivers.push({
            ...tour,
            driverName,
            driverFirstName,
            driverLastName,
          });
        } else {
          // Ajouter quand même la tournée sans chauffeur
          toursWithDrivers.push({
            ...tour,
            driverName: 'Non assigné',
            driverFirstName: '',
            driverLastName: '',
          });
        }
      });

      dispatch(setTours(toursWithDrivers));
    } catch (error) {
      console.error('❌ [ReceptionistPreloadTourList] Erreur lors du chargement:', error);
      dispatch(setError('Erreur lors du chargement des tournées et assignations'));
      presentToast({
        message: 'Erreur lors du chargement des tournées',
        duration: 3000,
        color: 'danger',
      });
    }
  };

  const handleTourClick = (tour: TourWithDriver) => {
    // TODO: Ouvrir le modal de validation
    console.log('Tour clicked:', tour);
  };

  const getTourStatusColor = (_tour: TourWithDriver) => {
    // TODO: Implémenter la logique de couleur selon l'état de validation
    return 'medium';
  };

  if (isLoading) {
    return <Loading />;
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <IonText className="text-red-500 text-center">
          <p className="text-lg font-semibold">Erreur</p>
          <p className="text-sm">{error}</p>
        </IonText>
        <IonButton fill="clear" size="small" onClick={loadToursAndAssignments} className="mt-2">
          Réessayer
        </IonButton>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 h-full mx-1 overflow-auto pt-4 sm:pt-6 md:pt-8 lg:pt-10 pb-10 sm:pb-12 md:pb-14 lg:pb-16">
      <div className="max-w-7xl mx-auto pt-2 sm:pt-4 md:pt-6 flex flex-col h-full px-2 sm:px-4 md:px-6 lg:px-8">
        {/* Header */}
        <div className="my-2 mb-6 sm:mb-8 md:mb-10 flex flex-col justify-between gap-2 sm:gap-4">
          <div className="flex-1">
            <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-neutral-500 dark:text-neutral-300">
              Bonjour {currentUser?.firstName || '--'}
            </p>
            <p className="text-sm sm:text-base text-gray-600 mt-1">
              Contrôlez et validez les agrès de toutes les tournées
            </p>

            {/* Input de recherche */}
            <SearchInput
              value={searchTerm}
              onValueChange={(value) => dispatch(setSearchTerm(value))}
              placeholder="Rechercher une tournée ou un chauffeur..."
              className="mt-4"
            />
          </div>
        </div>

        {/* Segments pour les types de tournées */}
        <div className="mb-6">
          <IonSegment
            value={currentSegment}
            onIonChange={(e) => dispatch(setCurrentSegment(e.detail.value as 'preload' | 'return'))}
            className="bg-white rounded-lg shadow-sm h-16"
          >
            <IonSegmentButton value="preload" className="border-neutral-200 border-l-2 border-r-2">
              <IonLabel>Pré-chargement ({departureTours.length})</IonLabel>
            </IonSegmentButton>
            <IonSegmentButton value="return" className="border-neutral-200 border-r-2">
              <IonLabel>Retour camion ({returnTours.length})</IonLabel>
            </IonSegmentButton>
          </IonSegment>
        </div>

        {/* Liste des tournées filtrées */}
        {filteredTours.length > 0 && (
          <div className="space-y-3">
            {filteredTours.map((tour: TourWithDriver) => (
              <TourCard
                key={tour.id}
                tour={tour}
                onClick={() => handleTourClick(tour)}
                statusColor={getTourStatusColor(tour)}
                showDeliveryDate={true}
              />
            ))}
          </div>
        )}

        {/* Espace pour les boutons flottants */}
        <div className="pb-20"></div>

        {/* Message si aucune tournée pour le segment actuel */}
        {(currentSegment === 'preload' ? departureTours : returnTours).length === 0 && (
          <div className="flex flex-col items-center justify-center h-64">
            <IonText className="text-gray-500 text-center">
              <p className="text-lg font-semibold">
                Aucune tournée {currentSegment === 'preload' ? 'à pré-charger' : 'finalisée'}
              </p>
            </IonText>
          </div>
        )}

        {/* Message si aucun résultat de recherche */}
        {(currentSegment === 'preload' ? departureTours : returnTours).length > 0 &&
          filteredTours.length === 0 &&
          searchTerm.trim() && (
            <div className="flex flex-col items-center justify-center h-64">
              <IonText className="text-gray-500 text-center">
                <p className="text-lg font-semibold">Aucune tournée trouvée</p>
                <p className="text-sm">Aucune tournée ne correspond à "{searchTerm}"</p>
                <IonButton
                  fill="clear"
                  size="small"
                  onClick={() => dispatch(clearSearchTerm())}
                  className="mt-2 text-blue-600"
                >
                  Effacer la recherche
                </IonButton>
              </IonText>
            </div>
          )}
      </div>
    </div>
  );
}
