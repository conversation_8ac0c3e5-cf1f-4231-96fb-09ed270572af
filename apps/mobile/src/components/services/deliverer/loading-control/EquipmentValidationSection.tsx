/**
 * @file EquipmentValidationSection.tsx
 * @description Composant pour la validation des équipements d'un arrêt
 * dans le contexte du contrôle de chargement
 */

import { IStopEntity } from '../../../../interfaces/entity/i-stop-entity';
import { StopCard } from '../../../ui/shared/StopCard';
import EquipmentValidator, { EquipmentItem } from '../EquipmentValidator';

interface EquipmentValidationSectionProps {
  stop: IStopEntity;
  equipmentItems: EquipmentItem[];
  onEquipmentChange: (items: EquipmentItem[]) => void;
  onValidationChange: (isValid: boolean) => void;
  onToggleAllEquipments: (isChecked: boolean) => void;
}

/**
 * Composant principal pour la section de validation des équipements
 */
export const EquipmentValidationSection: React.FC<EquipmentValidationSectionProps> = ({
  stop,
  equipmentItems,
  onEquipmentChange,
  onValidationChange,
  onToggleAllEquipments,
}) => {
  return (
    <StopCard
      stop={stop}
      equipmentItems={equipmentItems}
      onToggleValidation={onToggleAllEquipments}
    >
      <EquipmentValidator
        key={`${stop.id}-equipment-validator`}
        equipmentItems={equipmentItems}
        onEquipmentChange={onEquipmentChange}
        onValidationChange={onValidationChange}
        preloadedData={stop.completion?.preloadedEquipmentCount}
      />
    </StopCard>
  );
};
