import {CheckCir<PERSON>, Clock, Euro, Eye, Slash} from 'lucide-react';
import { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { formatAddress, isStopCompleted } from '../utils';
import TruncatedText from '../../ui/stylized/TruncatedText';
import { PATHS } from '../../../config/routes';
import { IStopEntity } from '../../../interfaces/entity/i-stop-entity';
import { ITourEntity } from '../../../interfaces/entity/i-tour-entity';
import { TourStatus } from '../../../interfaces/enum/tour.enums';
import DirectionsButton from '../../ui/DirectionsButton';
import { deliveryFormPersistenceService } from '../../../services/DeliveryFormPersistenceService';

interface TourDeliveryStopItemProps {
  stop: IStopEntity;
  tour: ITourEntity;
  onViewDetails?: () => void;
}

export default function TourDeliveryStopItem({
  stop,
  tour,
  onViewDetails,
}: TourDeliveryStopItemProps) {
  const history = useHistory();
  const isCompleted = isStopCompleted(stop);

  // État pour vérifier si un formulaire est en cache pour cet arrêt
  const [hasFormInCache, setHasFormInCache] = useState(false);

  // Vérifier si un formulaire est en cache pour cet arrêt
  useEffect(() => {
    const checkFormCache = async () => {
      try {
        const isPartiallyFilled = await deliveryFormPersistenceService.isFormPartiallyFilled(
          stop.id,
        );

        setHasFormInCache(isPartiallyFilled);
      } catch (error) {
        console.error('Erreur lors de la vérification du cache:', error);
        setHasFormInCache(false);
      }
    };

    checkFormCache();
  }, [stop.id]);

  // Déterminer l'état du bouton
  const getButtonState = () => {
    if (isCompleted) {
      return { type: 'completed', disabled: false };
    }

    if (tour.status === TourStatus.Completed) {
      return { type: 'cancelled', disabled: true };
    }

    if (hasFormInCache) {
      return { type: 'inProgress', disabled: false };
    }

    return { type: 'pending', disabled: false };
  };

  // Obtenir le style selon l'état
  const getButtonStyle = (buttonState: ReturnType<typeof getButtonState>) => {
    switch (buttonState.type) {
      case 'completed':
        return 'bg-green-600 text-white';
      case 'cancelled':
        return 'bg-red-500 text-white opacity-50 cursor-not-allowed';
      case 'inProgress':
        return 'bg-gray-500 text-white hover:bg-gray-600';
      case 'pending':
      default:
        return 'bg-yellow-500 text-white hover:bg-yellow-600';
    }
  };

  // Obtenir le texte selon l'état
  const getButtonText = (buttonState: ReturnType<typeof getButtonState>) => {
    switch (buttonState.type) {
      case 'completed':
        return 'TERMINÉ';
      case 'cancelled':
        return 'ANNULÉ';
      case 'inProgress':
        return 'EN COURS';
      case 'pending':
      default:
        return 'ARRIVÉE';
    }
  };

  const buttonState = getButtonState();

  const handleArrive = () => {
    // Ne pas permettre l'arrivée si le stop est déjà terminé ou désactivé
    if (isCompleted || buttonState.disabled) {
      return;
    }

    history.push(PATHS.STOP_DELIVERY.replace(':stopId', stop.id));
  };

  const handleViewClick = () => {
    if (onViewDetails) {
      onViewDetails();
    } else {
      handleArrive();
    }
  };

  const handleTourClick = () => {
    history.push(PATHS.TOURS);
  };

  function isPaid() {
    return stop.shipmentLines.some(
      (line) => line.amount ? line.amount > 0 : false,
    );
  }

  return (
    <div
      className={`border border-gray-300 rounded-md p-3 cursor-pointer sm:p-4 md:p-5 flex flex-col gap-3 sm:gap-4 ${
        isCompleted ? 'bg-green-50 border-green-200' : ''
      }`}
    >
      <div className="flex flex-row justify-between items-center">
        <p
          className={`text-base sm:text-lg md:text-xl font-bold ${isCompleted ? 'text-green-600' : 'primary-color'}`}
        >
          <TruncatedText text={stop.originalClientInfo?.name || ''} maxLength={15} />
        </p>
        <div className="flex flex-row justify-between items-center gap-1 sm:gap-2">
          <div
            className="text-sm sm:text-base md:text-lg text-gray-500 flex flex-row items-center gap-1 sm:gap-2 cursor-pointer"
            onClick={handleViewClick}
          >
            <Eye className="text-gray-500 w-4 h-4 sm:w-5 sm:h-5" />
            <p className="text-sm sm:text-base md:text-lg text-gray-500">Voir</p>
          </div>
          <span className="text-gray-500 text-sm sm:text-base">|</span>
          <div className="text-sm sm:text-base md:text-lg text-gray-500 flex flex-row items-center gap-1 sm:gap-2">
            <Clock className="text-gray-500 w-4 h-4 sm:w-5 sm:h-5" />
            <p className="text-sm sm:text-base md:text-lg text-gray-500">
              {stop.deliveryTimeWindow || '--:--'}
            </p>
          </div>
        </div>
      </div>

      <div className="flex flex-row justify-between items-center gap-2 sm:gap-4">
        <p className="text-xs sm:text-sm md:text-base text-gray-500 flex-1">
          {formatAddress(stop.originalClientInfo?.address || {})}
        </p>
        <div className="flex justify-end flex-1">
          {isCompleted ? (
            <div className="bg-green-600 text-white rounded-full p-1 sm:p-2 px-2 sm:px-4 font-bold flex items-center gap-1 sm:gap-2">
              <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4" />
              <p className="text-white text-xs sm:text-sm md:text-base">TERMINÉ</p>
            </div>
          ) : (
            <div
              onClick={buttonState.disabled ? undefined : handleArrive}
              className={`rounded-full p-1 sm:p-2 px-2 sm:px-4 font-bold transition-colors ${
                buttonState.disabled ? '' : 'cursor-pointer'
              } ${getButtonStyle(buttonState)}`}
            >
              {buttonState.type === 'completed' && (
                <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 inline mr-1" />
              )}
              <p className="text-xs sm:text-sm md:text-base">{getButtonText(buttonState)}</p>
            </div>
          )}
        </div>
      </div>

      <div className="flex flex-row justify-between items-center">
        <div className="text-xs text-gray-500 flex-1 flex flex-row items-center gap-1 sm:gap-2">
          {isPaid() ? ( <Euro className="text-gray-500 w-4 h-4 sm:w-5 sm:h-5 primary-color" />) : (
              <div className={'relative'}>
                <Euro className="absolute text-gray-500 w-4 h-4 sm:w-5 sm:h-5 primary-color" />
                <Slash className="absolute text-gray-500 w-4 h-4 sm:w-5 sm:h-5 primary-color" />
              </div>
          )}
          <span className="text-gray-500 text-xs sm:text-sm">|</span>
          <span
            className="text-sm sm:text-base md:text-lg text-gray-500 cursor-pointer hover:underline"
            onClick={handleTourClick}
          >
            Tournée {tour.tourIdentifier?.originalNumber}
          </span>
        </div>
        <div className="flex flex-row justify-end items-center gap-1 sm:gap-2 flex-1">
          {/* Bouton itinéraire */}
          <DirectionsButton
            addressLines={stop.originalClientInfo?.address}
            size="small"
            fill="ghost"
            color="primary"
            className="w-auto"
          >
            Itinéraire
          </DirectionsButton>
        </div>
      </div>
    </div>
  );
}
