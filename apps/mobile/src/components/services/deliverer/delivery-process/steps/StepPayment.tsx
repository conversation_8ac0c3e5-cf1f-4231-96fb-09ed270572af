import { IonCheckbox, IonItem, IonText } from '@ionic/react';
import { useState, forwardRef, useImperativeHandle } from 'react';
import { IStopEntity } from '../../../../../interfaces/entity/i-stop-entity';
import InlineButtons from '../../../../ui/stylized/InlineButtons';
import { ClientCard } from '../../../../ui/shared/ClientCard';
import { PaymentData } from '../types';
import { Loading } from "../../../../layout/Loading";

interface StepPaymentProps {
  stop: IStopEntity;
  onNext: (paymentData: PaymentData) => void;
  initialPaymentData?: PaymentData;
}

export interface StepPaymentRef {
  submit: () => void;
  isValid: () => boolean;
}

export enum Devise {
  CHF = 'CHF',
  EUR = 'EUR',
}

const StepPayment = forwardRef<StepPaymentRef, StepPaymentProps>(({ stop, onNext, initialPaymentData }, ref) => {
  const [isPaid, setIsPaid] = useState(initialPaymentData?.isPaid || false);

  const totalAmount =
    stop.shipmentLines?.reduce((total, line) => total + (line.amount || 0), 0) || 0;
  const hasPaymentData = totalAmount > 0;
  const devise = Devise.CHF;

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('fr-CH', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const paidAmount = isPaid ? totalAmount : 0;
  const amountToSettle = isPaid ? 0 : totalAmount;

  const handlePaymentToggle = (checked: boolean) => {
    setIsPaid(checked);
  };

  const handleNext = () => {
    if (!hasPaymentData) {
      // Pour le cas "no-data", on passe simplement à l'étape suivante
      onNext({
        totalAmount: 0,
        paidAmount: 0,
        remainingAmount: 0,
        isPaid: true, // Considéré comme payé car il n'y a rien à payer
        hasPaymentData: false,
      });

      return;
    }

    if (isPaid) {
      const paymentData: PaymentData = {
        totalAmount,
        paidAmount,
        remainingAmount: amountToSettle,
        isPaid,
        hasPaymentData: true,
      };
      onNext(paymentData);
    }
  };

  const isFormValid = () => {
    if (!hasPaymentData) return true; // Si pas de données de paiement, considéré comme valide
    return isPaid; // Le paiement doit être marqué comme payé
  };

  useImperativeHandle(ref, () => ({
    submit: handleNext,
    isValid: isFormValid,
  }));

  if (!hasPaymentData) {
    return (
      <Loading />
    );
  }

  return (
    <div className="ion-padding pt-6 pb-6">
      <ClientCard stop={stop} className={'mb-4'} />
      <div className="mb-6 mt-10">
        <div className="flex justify-between items-center">
          <IonText className="text-base sm:text-lg md:text-xl font-bold text-black">
            À régler
          </IonText>
          <IonText className="text-base sm:text-lg md:text-xl font-semibold text-black">
            {formatAmount(amountToSettle)} {devise}
          </IonText>
        </div>
      </div>

      <div className="mb-8 flex justify-between items-center">
        <IonItem lines="none" className="ion-no-padding">
          <IonCheckbox
            slot="start"
            checked={isPaid}
            onIonChange={(e) => handlePaymentToggle(e.detail.checked)}
            className="mr-3"
            color="success"
          />
          <IonText className="text-base sm:text-lg">Payé</IonText>
        </IonItem>
        <IonText className="text-base sm:text-lg md:text-xl font-semibold text-black">
          {formatAmount(paidAmount)} {devise}
        </IonText>
      </div>

      {isPaid && (
        <div className="mb-8 p-4 rounded-lg bg-green-100 border border-green-200">
          <IonText className="text-base sm:text-lg md:text-xl font-semibold text-green-800">
            Montant payé: {formatAmount(paidAmount)} {devise}
          </IonText>
        </div>
      )}
    </div>
  );
});

export default StepPayment;
