import { IonIcon, IonText } from '@ionic/react';
import { cube, snow } from 'ionicons/icons';
import { IStopEntity } from '../../../../../interfaces/entity/i-stop-entity';
import { formatAddress } from '../../../utils';
import InlineButtons from '../../../../ui/stylized/InlineButtons';
import { Weight } from "lucide-react";
import { ClientCard } from "../../../../ui/shared/ClientCard";

interface StepDeliveryProps {
  stop: IStopEntity;
  onNext: () => void;
  onClientAbsent: () => void;
}

const StepDelivery: React.FC<StepDeliveryProps> = ({ stop, onNext, onClientAbsent }) => {
  const { originalClientInfo, shipmentLines } = stop;

  const formatWeightKg = (weight?: number) => {
    return weight ? `${weight} KG` : 'N/A';
  };

  return (
    <div className="ion-padding pt-6 pb-6">
      <ClientCard stop={stop} className="mb-4" />

      <div className="mb-24 flex items-center gap-4 justify-between">
        <div className="flex items-center gap-2">
          <Weight size={16} className={ "text-gray-500" } />
          <IonText className="font-semibold text-gray-500 text-sm sm:text-base">
            {formatWeightKg(shipmentLines?.[0]?.weightKg)}
          </IonText>
        </div>
        {shipmentLines?.[0]?.isFrozen && (
          <div className="flex items-center gap-2">
            <IonIcon icon={snow} color="medium" size="small" className="text-gray-500" />
            <IonText className="font-medium text-gray-500 text-sm sm:text-base">SURGELÉS</IonText>
          </div>
        )}
      </div>

      <div className="space-y-3 flex flex-col gap-4 justify-center items-center">
        <InlineButtons
          buttons={[

            {
              label: 'Client absent',
              onClick: onClientAbsent,
              classNames: {
                button: 'primary-button-outline w-fit',
              },
            },
          ]}
        />
      </div>
    </div>
  );
};

export default StepDelivery;
