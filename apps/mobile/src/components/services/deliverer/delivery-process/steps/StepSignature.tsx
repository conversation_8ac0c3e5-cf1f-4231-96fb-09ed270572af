import {
  IonButton,
  IonIcon,
  IonInput,
  IonItem,
  IonModal,
  IonText,
  useIonToast,
} from '@ionic/react';
import { checkmarkCircle, pencil, save } from 'ionicons/icons';
import { useState, useMemo, forwardRef, useImperativeHandle } from 'react';
import { IStopEntity } from '../../../../../interfaces/entity/i-stop-entity';
import SignatureCapture from '../../../../ui/SignatureCapture';
import InlineButtons from '../../../../ui/stylized/InlineButtons';
import { formatAddress } from '../../../utils';
import { ClientCard } from '../../../../ui/shared/ClientCard';

interface StepSignatureProps {
  stop: IStopEntity;
  onNext: (signatureData: SignatureData) => void;
  initialSignatureData?: SignatureData;
}

export interface StepSignatureRef {
  submit: () => void;
  isValid: () => boolean;
}

export interface SignatureData {
  hasSignature: boolean;
  signatureBase64: string;
  email: string;
  firstName: string;
  lastName: string;
  signatureFile?: {
    base64: string;
    filename: string;
    mimeType: string;
  };
}

const StepSignature = forwardRef<StepSignatureRef, StepSignatureProps>(({ stop, onNext, initialSignatureData }, ref) => {
  const [presentToast] = useIonToast();

  const [signatureBase64, setSignatureBase64] = useState<string>(
    initialSignatureData?.signatureBase64 || '',
  );
  const [showSignatureModal, setShowSignatureModal] = useState(false);

  // Préremplir l'email avec les données persistées ou l'email du client
  const getInitialEmail = () => {
    return stop.originalClientInfo?.email || '';
  };

  const [email, setEmail] = useState(getInitialEmail());
  const [firstName, setFirstName] = useState(initialSignatureData?.firstName || '');
  const [lastName, setLastName] = useState(initialSignatureData?.lastName || '');

  const isFormValid = useMemo(() => {
    const isEmailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

    return (
      signatureBase64.length > 0 &&
      isEmailValid &&
      firstName.trim() !== '' &&
      lastName.trim() !== ''
    );
  }, [signatureBase64, email, firstName, lastName]);

  const handleSignatureCapture = (signature: string) => {
    setSignatureBase64(signature);
    setShowSignatureModal(false);
    presentToast({
      message: 'Signature enregistrée avec succès.',
      duration: 2000,
      color: 'success',
      icon: checkmarkCircle,
    });
  };

  const handleSign = () => {
    if (!isFormValid) {
      let errorMessage = 'Veuillez remplir tous les champs et capturer la signature.';

      if (signatureBase64.length === 0) {
        errorMessage = 'La signature est obligatoire.';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        errorMessage = 'Veuillez entrer une adresse email valide.';
      }
      presentToast({
        message: errorMessage,
        duration: 3000,
        color: 'warning',
      });

      return;
    }

    const signatureData: SignatureData = {
      hasSignature: true,
      signatureBase64,
      email: email.trim(),
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      signatureFile: {
        base64: signatureBase64.replace(/^data:image\/[a-z]+;base64,/, ''),
        filename: `sign_${stop.id}.png`,
        mimeType: 'image/png',
      },
    };

    onNext(signatureData);
  };

  useImperativeHandle(ref, () => ({
    submit: handleSign,
    isValid: () => isFormValid,
  }));

  return (
    <div className="pt-6 pb-6 ">
      {/* Header responsive */}
      <ClientCard stop={stop} className={'mb-4'} />

      {/* Section signature obligatoire */}
      <div className="mb-6 sm:mb-8">
        <div className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6">
          Signature du destinataire
        </div>

        <IonButton
          expand="block"
          fill="outline"
          color={signatureBase64 ? 'success' : 'primary'}
          onClick={() => setShowSignatureModal(true)}
          className="h-12 sm:h-14 md:h-16 rounded-lg"
        >
          <div className="flex items-center gap-2 justify-center">
            <IonIcon icon={signatureBase64 ? save : pencil} />
            <span className="text-sm sm:text-base md:text-lg font-semibold">
              {signatureBase64 ? 'SIGNATURE ENREGISTRÉE' : 'SIGNER'}
            </span>
          </div>
        </IonButton>
      </div>

      {/* Section informations client responsive */}
      <div className="mb-8 sm:mb-10 md:mb-12">
        <div className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 sm:mb-6">
          Informations client
        </div>

        <div className="space-y-4 sm:space-y-5 md:space-y-6">
          {/* Email */}
          <div>
            <IonText className="text-xs sm:text-sm md:text-base font-medium text-gray-700 block mb-2">
              EMAIL CLIENT
            </IonText>
            <IonItem className="border border-gray-300 rounded-lg h-12 sm:h-14 md:h-16">
              <IonInput
                type="email"
                value={email}
                onIonInput={(e) => setEmail(e.detail.value || '')}
                placeholder="<EMAIL>"
                className="text-sm sm:text-base md:text-lg"
              />
            </IonItem>
          </div>

          {/* Prénom */}
          <div>
            <IonText className="text-xs sm:text-sm md:text-base font-medium text-gray-700 block mb-2">
              PRÉNOM
            </IonText>
            <IonItem className="border border-gray-300 rounded-lg h-12 sm:h-14 md:h-16">
              <IonInput
                type="text"
                value={firstName}
                onIonInput={(e) => setFirstName(e.detail.value || '')}
                placeholder="Prénom du destinataire"
                className="text-sm sm:text-base md:text-lg"
              />
            </IonItem>
          </div>

          {/* Nom */}
          <div>
            <IonText className="text-xs sm:text-sm md:text-base font-medium text-gray-700 block mb-2">
              NOM
            </IonText>
            <IonItem className="border border-gray-300 rounded-lg h-12 sm:h-14 md:h-16">
              <IonInput
                type="text"
                value={lastName}
                onIonInput={(e) => setLastName(e.detail.value || '')}
                placeholder="Nom du destinataire"
                className="text-sm sm:text-base md:text-lg"
              />
            </IonItem>
          </div>
        </div>
      </div>



      <IonModal isOpen={showSignatureModal} onDidDismiss={() => setShowSignatureModal(false)}>
        <SignatureCapture
          onSignatureCapture={handleSignatureCapture}
          onCancel={() => setShowSignatureModal(false)}
        />
      </IonModal>
    </div>
  );
});

export default StepSignature;
