import {
  IonCard,
  IonCardContent,
  IonIcon,
  IonModal,
  IonText,
  IonTextarea,
  useIonToast,
} from '@ionic/react';
import { camera, warning } from 'ionicons/icons';
import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import PhotoCapture from '../../../../ui/PhotoCapture';
import InlineButtons from '../../../../ui/stylized/InlineButtons';
import { formatAddress } from '../../../utils';
import { IStopEntity } from '../../../../../interfaces/entity/i-stop-entity';
import { ClientCard } from "../../../../ui/shared/ClientCard";

interface CameraDocumentationProps {
  stationName?: string;
  stop: IStopEntity;
  onNext: (photos: string[], comment: string) => void;
  onPrevious?: () => void;
  // Nouvelles props pour la persistance
  initialPhotos?: string[];
  initialComment?: string;
}

export interface StepClientCommentRef {
  submit: () => void;
  isValid: () => boolean;
}

export interface PhotoDocumentationData {
  hasPhoto: boolean;
  photoBase64: string;
  comment: string;
  isSecureLocation: boolean;
  // Format pour backend
  photoFile?: {
    base64: string;
    filename: string;
    mimeType: string;
  };
}

const StepClientComment = forwardRef<StepClientCommentRef, CameraDocumentationProps>(({
  stop,
  onNext,
  initialPhotos = [],
  initialComment = '',
}, ref) => {
  const [presentToast] = useIonToast();

  // États simplifiés
  const [showPhotoModal, setShowPhotoModal] = useState(false);
  const [comment, setComment] = useState(initialComment);
  const [photos, setPhotos] = useState<string[]>([]);
  const [isSecureLocation, setIsSecureLocation] = useState(false);

  // Initialiser les photos
  useEffect(() => {
      const photoSlots = [...initialPhotos];
      // Remplir les emplacements vides jusqu'à 4 photos
      while (photoSlots.length < 4) {
        photoSlots.push('');
      }

      setPhotos(photoSlots);
  }, [initialPhotos]);

  // Calculer hasPhoto depuis le tableau photos
  const hasPhoto = photos.some(photo => photo !== '');
  const firstPhoto = photos.find(photo => photo !== '') || '';

  // Détection lieu sûr au démarrage (logique de StopCompletion)
  useEffect(() => {
    const clientCode = stop?.originalClientInfo?.code || '';
    setIsSecureLocation(clientCode.includes('SECURE'));
  }, [stop]);

  // Handler de capture photo (simplifié)
  const handlePhotoCapture = (photo: string) => {
    setShowPhotoModal(false);

    // Ajouter la photo au premier emplacement vide
    const updatedPhotos = [...photos];
    const firstEmptyIndex = updatedPhotos.findIndex((p) => p === '');

    console.log('🔍 [StepClientComment] handlePhotoCapture - current photos:', photos);
    console.log('🔍 [StepClientComment] handlePhotoCapture - new photo length:', photo.length);
    console.log('🔍 [StepClientComment] handlePhotoCapture - first empty index:', firstEmptyIndex);

    if (firstEmptyIndex !== -1) {
      updatedPhotos[firstEmptyIndex] = photo;
      setPhotos(updatedPhotos);
      
      console.log('🔍 [StepClientComment] handlePhotoCapture - updated photos:', updatedPhotos);

      presentToast({
        message: 'Photo enregistrée',
        duration: 2000,
        color: 'success',
      });
    } else {
      presentToast({
        message: 'Tous les emplacements photo sont occupés',
        duration: 2000,
        color: 'warning',
      });
    }
  };

  // Validation selon logique StopCompletion
  const validateForm = (): string | null => {
    // Pour lieu sûr, photo obligatoire
    if (isSecureLocation && !hasPhoto) {
      return 'Une photo est obligatoire pour les livraisons en lieu sûr';
    }

    // Au moins une photo recommandée pour documentation
    if (!hasPhoto && photos.every((p) => p === '')) {
      return 'Une photo de preuve est recommandée';
    }

    return null;
  };

  const handleNext = () => {
    const validationError = validateForm();

    if (validationError && isSecureLocation) {
      presentToast({
        message: validationError,
        duration: 4000,
        color: 'warning',
      });

      return;
    }

    // Filtrer les photos non vides
    const allPhotos = photos.filter((p) => p !== '');
    
    // Debug pour voir ce qui est envoyé
    console.log('🔍 [StepClientComment] handleNext - photos state:', photos);
    console.log('🔍 [StepClientComment] handleNext - filtered photos:', allPhotos);
    console.log('🔍 [StepClientComment] handleNext - comment:', comment.trim());

    onNext(allPhotos, comment.trim());
  };

  const isFormValidForButton = () => {
    // Pour lieu sûr, photo obligatoire
    if (isSecureLocation && !hasPhoto) {
      return false;
    }
    // Sinon, le formulaire est valide (photos et commentaires sont optionnels)
    return true;
  };

  useImperativeHandle(ref, () => ({
    submit: handleNext,
    isValid: isFormValidForButton,
  }));

  const handlePhotoClick = (index: number) => {
    // Si la photo est vide, ouvrir la caméra
    if (photos[index] === '') {
      setShowPhotoModal(true);
    }
  };

  const handlePhotoDelete = (index: number) => {
    const updatedPhotos = [...photos];
    updatedPhotos[index] = '';
    setPhotos(updatedPhotos);

    presentToast({
      message: 'Photo supprimée',
      duration: 2000,
      color: 'medium',
    });
  };

  return (
    <div className="pt-6 pb-6">
      <ClientCard stop={stop} className="mb-4" />
      {/* Section avec bouton caméra central du design original */}
      <div className="flex justify-center mb-6 pt-6">
        <div
          className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 bg-black rounded-full flex items-center justify-center cursor-pointer"
          onClick={() => setShowPhotoModal(true)}
        >
          <IonIcon icon={camera} className="text-white text-xl sm:text-2xl md:text-3xl" />
        </div>
      </div>

      {/* Section avec les 4 photos du design original */}
      <div className="grid grid-cols-4 gap-2 sm:gap-4 md:gap-6 mb-6 sm:mb-8">
        {photos.map((photo, index) => (
          <div
            key={index}
            className="relative aspect-square border-2 border-gray-300 rounded-lg flex items-center justify-center cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors"
            onClick={() => handlePhotoClick(index)}
          >
            {photo && photo !== '' ? (
              <>
                <img
                  src={photo}
                  alt={`Photo ${index + 1}`}
                  className="w-full h-full object-cover rounded-lg"
                />
                {/* Pastille rouge de suppression en absolute à droite */}
                <button
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg z-10 hover:bg-red-600 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation(); // Empêcher le clic sur la photo
                    handlePhotoDelete(index);
                  }}
                >
                  ×
                </button>
              </>
            ) : (
              <div className="text-gray-400 text-lg sm:text-2xl md:text-3xl">×</div>
            )}
          </div>
        ))}
      </div>

      {/* Section Commentaire du design original */}
      <div className="mb-8 sm:mb-10 md:mb-12">
        <IonText className="text-sm sm:text-base md:text-lg font-bold text-gray-800 block mb-3 sm:mb-4">
          COMMENTAIRE
        </IonText>
        <IonTextarea
          placeholder="Ajouter des commentaires sur la livraison..."
          value={comment}
          onIonInput={(e) => setComment(e.detail.value || '')}
          rows={4}
          className="border border-gray-300 rounded-lg bg-white text-sm sm:text-base md:text-lg"
          fill="outline"
        />
      </div>



      {/* Modal Photo (logique backend) */}
      <IonModal isOpen={showPhotoModal} onDidDismiss={() => setShowPhotoModal(false)}>
        <PhotoCapture
          onPhotoCapture={handlePhotoCapture}
          onCancel={() => setShowPhotoModal(false)}
        />
      </IonModal>
    </div>
  );
});

export default StepClientComment;
