import { IonText, useIonToast } from '@ionic/react';
import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { IStopEntity } from '../../../../../interfaces/entity/i-stop-entity';
import InlineButtons from '../../../../ui/stylized/InlineButtons';
import { EquipmentValidationSection } from '../../loading-control/EquipmentValidationSection';
import { EquipmentItem } from '../../EquipmentValidator';
import {
  IEquipmentCountDto,
  ILogisticsEquipmentDetailDto,
} from '../../../../../interfaces/dto/stop-delivery.dto';
import { LogisticsEquipmentKind } from '../../../../../interfaces/enum/logistics-equipment-kind.enum';
import { formatAddress } from '../../../utils';

export interface UnloadedEquipmentData {
  unloadedEquipment: IEquipmentCountDto;
  unloadedEquipmentDetails: ILogisticsEquipmentDetailDto[];
}

interface StepEquipmentDepositProps {
  stop: IStopEntity;
  onNext: (unloadedEquipmentData: UnloadedEquipmentData) => void;
  onPrevious?: () => void;
  initialEquipmentData?: UnloadedEquipmentData;
}

export interface StepEquipmentDepositRef {
  submit: () => void;
  isValid: () => boolean;
}

const StepEquipmentDeposit = forwardRef<StepEquipmentDepositRef, StepEquipmentDepositProps>(({
  stop,
  onNext,
  initialEquipmentData,
}, ref) => {
  const [isFormValid, setIsFormValid] = useState(false);
  const [presentToast] = useIonToast();
  const [equipmentItems, setEquipmentItems] = useState<EquipmentItem[]>([]);

  // Convertit les données initiales en EquipmentItem
  const convertInitialDataToEquipmentItems = (
    initialData: UnloadedEquipmentData,
  ): EquipmentItem[] => {
    const items: EquipmentItem[] = [];
    const { unloadedEquipment } = initialData;

    // Toujours créer les 3 types standard
    const standardKinds = [
      LogisticsEquipmentKind.PALLET,
      LogisticsEquipmentKind.ROLL,
      LogisticsEquipmentKind.PACKAGE,
    ];

    standardKinds.forEach((kind) => {
      let quantity = 0;
      let isValidated = false;

      switch (kind) {
        case LogisticsEquipmentKind.PALLET:
          quantity = unloadedEquipment?.palletCount || 0;
          break;
        case LogisticsEquipmentKind.ROLL:
          quantity = unloadedEquipment?.rollCount || 0;
          break;
        case LogisticsEquipmentKind.PACKAGE:
          quantity = unloadedEquipment?.packageCount || 0;
          break;
      }

      if (quantity > 0) {
        isValidated = true;
      }

      items.push({
        type: kind,
        receivedQuantity: quantity,
        adjustedQuantity: quantity,
        isValidated,
      });
    });

    return items;
  };

  // Initialiser les équipements au montage du composant
  useEffect(() => {
    if (initialEquipmentData) {
      const items = convertInitialDataToEquipmentItems(initialEquipmentData);
      setEquipmentItems(items);
    } else {
      // Créer des items vides pour les types standard
      const items = [
        LogisticsEquipmentKind.PALLET,
        LogisticsEquipmentKind.ROLL,
        LogisticsEquipmentKind.PACKAGE,
      ].map((kind) => ({
        type: kind,
        receivedQuantity: 0,
        adjustedQuantity: 0,
        isValidated: false,
      }));
      setEquipmentItems(items);
    }
  }, [initialEquipmentData]);

  const handleValidationChange = (isValid: boolean) => {
    setIsFormValid(isValid);
  };

  const handleEquipmentChange = (items: EquipmentItem[]) => {
    setEquipmentItems(items);
  };

  const handleToggleAllEquipments = (isChecked: boolean) => {
    const updatedItems = equipmentItems.map((item) => ({
      ...item,
      isValidated: isChecked && item.adjustedQuantity >= 0,
    }));
    setEquipmentItems(updatedItems);
    setIsFormValid(updatedItems.some((item) => item.isValidated && item.adjustedQuantity >= 0));
  };

  const handleNext = () => {
    if (!isFormValid) {
      presentToast({
        message: 'Veuillez valider au moins un équipement avec une quantité.',
        duration: 3000,
        color: 'warning',
      });

      return;
    }

    try {
      // Convertir les EquipmentItem en UnloadedEquipmentData
      const unloadedEquipment: IEquipmentCountDto = {};
      const unloadedEquipmentDetails: ILogisticsEquipmentDetailDto[] = [];

      equipmentItems.forEach((item) => {
        if (item.isValidated && item.adjustedQuantity >= 0) {
          // Ajouter aux totaux par type (on n'a plus besoin des détails avec les IDs)
          switch (item.type) {
            case LogisticsEquipmentKind.PALLET:
              unloadedEquipment.palletCount =
                (unloadedEquipment?.palletCount || 0) + item.adjustedQuantity;
              break;
            case LogisticsEquipmentKind.ROLL:
              unloadedEquipment.rollCount =
                (unloadedEquipment?.rollCount || 0) + item.adjustedQuantity;
              break;
            case LogisticsEquipmentKind.PACKAGE:
              unloadedEquipment.packageCount =
                (unloadedEquipment?.packageCount || 0) + item.adjustedQuantity;
              break;
          }
        }
      });

      const unloadedEquipmentData: UnloadedEquipmentData = {
        unloadedEquipment,
        unloadedEquipmentDetails, // Vide pour l'instant
      };

      onNext(unloadedEquipmentData);
    } catch (error) {
      console.error('Erreur lors de la conversion des données:', error);
      presentToast({
        message: 'Erreur lors du traitement des données.',
        duration: 3000,
        color: 'danger',
      });
    }
  };
  const isCached = initialEquipmentData?.returnedEquipment && Object.keys(initialEquipmentData?.returnedEquipment).length > 0;

  useImperativeHandle(ref, () => ({
    submit: handleNext,
    isValid: () => isCached || isFormValid,
  }));

  return (
    <div className="pt-6 pb-6">
      <EquipmentValidationSection
        stop={stop}
        equipmentItems={equipmentItems}
        onEquipmentChange={handleEquipmentChange}
        onValidationChange={handleValidationChange}
        onToggleAllEquipments={handleToggleAllEquipments}
      />
    </div>
  );
});

export default StepEquipmentDeposit;
