import { IonText, useIonToast } from '@ionic/react';
import { useState, useEffect } from 'react';
import { IStopEntity } from '../../../../interfaces/entity/i-stop-entity';
import { ILogisticsEquipmentTypeEntity } from '../../../../interfaces/entity/i-logistics-equipment-type-entity';
import { logisticsEquipmentTypeService } from '../../../../services/LogisticsEquipmentTypeService';
import InlineButtons from '../../../ui/stylized/InlineButtons';
import EquipmentQuantitySelector, {
  EquipmentSelectorData,
  EquipmentQuantityData,
} from '../../receptionnist/LogisticEquipmentKindQuantitySelector';
import ClientInfo from './ClientInfo';

interface BaseEquipmentStepProps<T> {
  stop: IStopEntity;
  onNext: (equipmentData: T) => void;
  onPrevious?: () => void;
  initialEquipmentData?: T;
  convertToQuantities: (
    data: T,
    equipmentTypes: ILogisticsEquipmentTypeEntity[],
  ) => Record<string, EquipmentQuantityData> | undefined;
  convertFromSelectorData: (selectorData: EquipmentSelectorData) => T;
  title?: string;
}

function BaseEquipmentStep<T>({
  stop,
  onNext,
  initialEquipmentData,
  convertToQuantities,
  convertFromSelectorData,
}: BaseEquipmentStepProps<T>) {
  const [equipmentTypes, setEquipmentTypes] = useState<ILogisticsEquipmentTypeEntity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFormValid, setIsFormValid] = useState(false);
  const [presentToast] = useIonToast();
  const [equipmentData, setEquipmentData] = useState<EquipmentSelectorData | null>(null);

  const getInitialQuantities = () => {
    if (!initialEquipmentData || equipmentTypes.length === 0) {
      return undefined;
    }

    return convertToQuantities(initialEquipmentData, equipmentTypes);
  };

  useEffect(() => {
    const loadEquipmentTypes = async () => {
      try {
        setLoading(true);
        const response = await logisticsEquipmentTypeService.findAllForDeliver();

        let types: ILogisticsEquipmentTypeEntity[] = [];

        if (Array.isArray(response)) {
          types = response;
        } else if (response && typeof response === 'object' && 'items' in response) {
          types = (response as { items: ILogisticsEquipmentTypeEntity[] }).items;
        }

        setEquipmentTypes(types);
      } catch (err) {
        console.error("Erreur lors du chargement des types d'équipement:", err);
        setError("Impossible de charger les types d'équipement. Veuillez réessayer.");
        presentToast({
          message: "Erreur lors du chargement des types d'équipement",
          duration: 3000,
          color: 'danger',
        });
      } finally {
        setLoading(false);
      }
    };

    loadEquipmentTypes();
  }, [presentToast]);

  const handleValidationChange = (isValid: boolean) => {
    setIsFormValid(isValid);
  };

  const handleNext = () => {
    if (!isFormValid || !equipmentData) {
      presentToast({
        message: 'Veuillez valider au moins un équipement avec une quantité.',
        duration: 3000,
        color: 'warning',
      });

      return;
    }

    try {
      const convertedData = convertFromSelectorData(equipmentData);
      onNext(convertedData);
    } catch (error) {
      console.error('Erreur lors de la conversion des données:', error);
      presentToast({
        message: 'Erreur lors du traitement des données.',
        duration: 3000,
        color: 'danger',
      });
    }
  };

  if (loading) {
    return (
      <div className="pt-6 pb-6">
        <ClientInfo stop={stop} variant="medium" className="mb-10" />
        <div className="flex justify-center items-center h-32">
          <IonText className="text-gray-500">Chargement des équipements...</IonText>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="pt-6 pb-6">
        <ClientInfo stop={stop} variant="medium" className="mb-10" />
        <div className="flex justify-center items-center h-32">
          <IonText className="text-red-500">{error}</IonText>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-6 pb-6">
      <ClientInfo stop={stop} variant="medium" className="mb-10" />

      <EquipmentQuantitySelector
        equipmentTypes={equipmentTypes}
        initialQuantities={getInitialQuantities()}
        onValidationChange={handleValidationChange}
        onEquipmentDataChange={setEquipmentData}
        requireValidation={true}
        allowEmptyQuantities={false}
      />

      <div className="space-y-3 flex flex-col gap-4 justify-center items-center">
        <InlineButtons
          buttons={[
            {
              label: 'Suivant',
              onClick: handleNext,
              classNames: {
                button: `px-8 sm:px-12 py-3 sm:py-4 ${
                  isFormValid ? 'primary-button' : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`,
              },
            },
          ]}
        />
      </div>
    </div>
  );
}

export default BaseEquipmentStep;
