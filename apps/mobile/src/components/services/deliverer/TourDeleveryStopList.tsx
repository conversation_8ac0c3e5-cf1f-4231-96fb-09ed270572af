import {
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonIcon,
  IonModal,
  IonPage,
  IonTitle,
  IonToolbar,
  IonText,
} from '@ionic/react';
import { closeOutline } from 'ionicons/icons';
import { useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { isStopCompleted } from '../utils';
import TruncatedText from '../../ui/stylized/TruncatedText';
import { useTourControlledGuard } from '../../../hooks/useTourControlledGuard';
import { IStopEntity } from '../../../interfaces/entity/i-stop-entity';
import { ITourEntity } from '../../../interfaces/entity/i-tour-entity';
import { TourStatus } from '../../../interfaces/enum/tour.enums';
import { PATHS } from '../../../config/routes';
import { useAppSelector } from '../../../utils/redux';
import DeliveryStopItem from './TourDeliveryStopItem';
import TourDashboardDeliveryStopDetail from '../../../pages/admin/dashboard/deliverer/tour/TourDashboardDeliveryStopDetail';
import InlineButtons from '../../ui/stylized/InlineButtons';

const TourDeliveryStopList: React.FC = () => {
  const history = useHistory();
  const { tours } = useAppSelector((state) => state.tour);
  const isTourLoading = useAppSelector((state) => !state.tour.isDataResolved);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedStop, setSelectedStop] = useState<IStopEntity | null>(null);
  const [selectedTour, setSelectedTour] = useState<ITourEntity | null>(null);

  // Direct completion check - more immediate than the guard hook
  const hasPlannedTours = tours.some((tour) => tour.status === TourStatus.Planned);
  
  // Vérifier si le contrôle de chargement a été récemment effectué
  const isLoadingControlRecentlyCompleted = () => {
    const completed = localStorage.getItem('loadingControlCompleted');
    if (!completed) return false;
    
    const completedTime = parseInt(completed);
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000; // 5 minutes en millisecondes
    
    return (now - completedTime) < fiveMinutes;
  };

  useTourControlledGuard();

  const handleStopDetail = (stop: IStopEntity, tour: ITourEntity) => {
    setSelectedStop(stop);
    setSelectedTour(tour);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedStop(null);
    setSelectedTour(null);
  };

  // Filtrer les stops non terminés
  const getActiveStops = (tour: ITourEntity) => {
    return tour.stops.filter((stop) => !isStopCompleted(stop));
  };

  // Block access if loading control not completed, sauf si récemment validé
  if (!isTourLoading && hasPlannedTours && !isLoadingControlRecentlyCompleted()) {
    return (
      <IonPage className="pt-20 sm:pt-24 md:pt-28 lg:pt-32 pb-20 sm:pb-24 md:pb-28 lg:pb-32">
        <IonContent fullscreen className="ion-padding">
          <div className="flex flex-col items-center justify-center h-full">
              <p className="text-xl font-bold text-neutral-700 mb-4">
                Contrôle de chargement requis
              </p>
              <InlineButtons
                buttons={[
                  {
                    label: 'Vers le contrôle de chargement',
                    onClick: () => history.push(PATHS.LOADING_CONTROL),
                    classNames: {
                      label : 'text-lg font-semibold',
                      button: 'primary-button py-6 px-6'

                    },
                  },
                ]}
              />
          </div>
        </IonContent>
      </IonPage>
    );
  }

  // Show loading state
  if (isTourLoading) {
    return (
      <IonPage className="pt-20 sm:pt-24 md:pt-28 lg:pt-32 pb-20 sm:pb-24 md:pb-28 lg:pb-32">
        <IonContent fullscreen className="ion-padding">
          <div className="flex flex-col items-center justify-center h-full">
            <IonText>
              <p className="text-xl font-bold text-neutral-500">
                Chargement des tournées...
              </p>
            </IonText>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  return (
    <IonPage className="pt-20 sm:pt-24 md:pt-28 lg:pt-32 pb-20 sm:pb-24 md:pb-28 lg:pb-32">
      <IonContent fullscreen className="ion-padding">
        <div className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold mb-4 sm:mb-6 md:mb-8 px-2 sm:px-0">
          Tournées en cours
        </div>
        {tours.map((tour) => {
          const activeStops = getActiveStops(tour);

          // Ne pas afficher la tournée si tous les stops sont terminés
          if (activeStops.length === 0) {
            return null;
          }

          return (
            <div key={tour.id} className="flex flex-col gap-2 sm:gap-3 md:gap-4 px-2 sm:px-0">
              {activeStops.map((stop) => (
                <DeliveryStopItem
                  key={stop.id}
                  stop={stop}
                  tour={tour}
                  onViewDetails={() => handleStopDetail(stop, tour)}
                />
              ))}
            </div>
          );
        })}

        <IonModal isOpen={isModalOpen} onDidDismiss={closeModal}>
          <IonHeader>
            <IonToolbar>
              <IonTitle>
                <TruncatedText
                  text={selectedStop?.originalClientInfo?.name?.toUpperCase() || ''}
                  className="text-lg sm:text-xl md:text-2xl font-bold mx-4 sm:mx-6 md:mx-8 p-1 sm:p-2 py-2 sm:py-3 md:py-4 primary-color"
                  maxLength={30}
                />
              </IonTitle>
              <IonButtons slot="end">
                <IonButton onClick={closeModal}>
                  <IonIcon icon={closeOutline} />
                </IonButton>
              </IonButtons>
            </IonToolbar>
          </IonHeader>
          <IonContent>
            {selectedStop && selectedTour && (
              <TourDashboardDeliveryStopDetail tour={selectedTour} stop={selectedStop} />
            )}
          </IonContent>
        </IonModal>
      </IonContent>
    </IonPage>
  );
};

export default TourDeliveryStopList;
