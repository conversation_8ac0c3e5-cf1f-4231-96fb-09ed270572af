import { IonIcon, IonText } from '@ionic/react';
import { add, remove } from 'ionicons/icons';
import { useEffect, useState, useRef } from 'react';
import { LogisticsEquipmentKind } from '../../../interfaces/enum/logistics-equipment-kind.enum';

export interface EquipmentItem {
  type: LogisticsEquipmentKind;
  receivedQuantity: number;
  adjustedQuantity: number;
  isValidated: boolean;
}

export interface PreloadedEquipmentData {
  palletCount?: number;
  rollCount?: number;
  packageCount?: number;
}

export interface EquipmentValidatorProps {
  equipmentItems: EquipmentItem[];
  onEquipmentChange: (items: EquipmentItem[]) => void;
  onValidationChange: (isValid: boolean) => void;
  preloadedData?: PreloadedEquipmentData;
}

const getEquipmentDisplayName = (kind: LogisticsEquipmentKind): string => {
  switch (kind) {
    case LogisticsEquipmentKind.PALLET:
      return 'Palettes';
    case LogisticsEquipmentKind.ROLL:
      return 'Rolls';
    case LogisticsEquipmentKind.PACKAGE:
      return 'Caisses';
    case LogisticsEquipmentKind.CARTON:
      return 'Cartons';
    case LogisticsEquipmentKind.CHEP:
      return 'CHEP';
    default:
      return kind;
  }
};

const getPreloadedQuantityForEquipmentType = (
  type: LogisticsEquipmentKind,
  preloadedData?: PreloadedEquipmentData,
): number => {
  if (!preloadedData) {
    return 0;
  }

  switch (type) {
    case LogisticsEquipmentKind.PALLET:
      return preloadedData.palletCount || 0;
    case LogisticsEquipmentKind.ROLL:
      return preloadedData.rollCount || 0;
    case LogisticsEquipmentKind.PACKAGE:
      return preloadedData.packageCount || 0;
    default:
      return 0;
  }
};

const EquipmentValidator = ({
  equipmentItems,
  onEquipmentChange,
  onValidationChange,
  preloadedData,
}: EquipmentValidatorProps) => {
  const [items, setItems] = useState<EquipmentItem[]>(equipmentItems);
  const isSynchronizingRef = useRef(false);

  // Autocomplete quantities from preloaded data when preloadedData is provided
  useEffect(() => {
    if (preloadedData && equipmentItems.length > 0) {
      const updatedItems = equipmentItems.map((item) => {
        const preloadedQuantity = getPreloadedQuantityForEquipmentType(item.type, preloadedData);

        // Only autocomplete if there's preloaded data and current adjustedQuantity is 0
        if (preloadedQuantity > 0 && item.adjustedQuantity === 0) {
          return {
            ...item,
            adjustedQuantity: preloadedQuantity,
          };
        }

        return item;
      });

      // Check if any items were updated
      const hasUpdates = updatedItems.some(
        (item, index) => item.adjustedQuantity !== equipmentItems[index].adjustedQuantity,
      );

      if (hasUpdates) {
        isSynchronizingRef.current = true;
        setItems(updatedItems);
        onEquipmentChange(updatedItems);
        isSynchronizingRef.current = false;
      }
    }
  }, [preloadedData, equipmentItems, onEquipmentChange]);

  // Synchroniser avec les props uniquement si les données viennent de l'extérieur
  useEffect(() => {
    // Éviter la boucle infinie en comparant les données
    const itemsChanged = JSON.stringify(items) !== JSON.stringify(equipmentItems);

    if (itemsChanged) {
      isSynchronizingRef.current = true;
      setItems(equipmentItems);
      // On ne déclenche PAS les callbacks ici car c'est une synchronisation depuis les props
      isSynchronizingRef.current = false;
    }
  }, [equipmentItems]);

  // Notifier les changements SEULEMENT quand l'utilisateur fait des actions
  const notifyChanges = (updatedItems: EquipmentItem[]) => {
    if (isSynchronizingRef.current) {
      return; // Ne pas notifier pendant la synchronisation
    }

    // Un stop est validé seulement si TOUS les équipements avec quantité > 0 sont validés
    const itemsWithQuantity = updatedItems.filter((item) => item.adjustedQuantity > 0);
    const hasValidatedItems =
      itemsWithQuantity.length > 0 && itemsWithQuantity.every((item) => item.isValidated);
    onValidationChange(hasValidatedItems);
    onEquipmentChange(updatedItems);
  };

  const handleCheckboxChange = (index: number, isChecked: boolean) => {
    const updatedItems = [...items];
    updatedItems[index] = {
      ...updatedItems[index],
      isValidated: isChecked,
    };
    // check if the other items are validated
    const allItemsValidated = updatedItems.every((item) => item.isValidated );

    setItems(updatedItems);
    notifyChanges(updatedItems);
    onValidationChange(allItemsValidated);
  };

  const handleQuantityChange = (index: number, newQuantity: number) => {
    const updatedItems = [...items];
    updatedItems[index] = {
      ...updatedItems[index],
      adjustedQuantity: Math.max(0, newQuantity),
    };
    setItems(updatedItems);
    notifyChanges(updatedItems);
  };

  const incrementQuantity = (index: number) => {
    handleQuantityChange(index, items[index].adjustedQuantity + 1);
  };

  const decrementQuantity = (index: number) => {
    handleQuantityChange(index, items[index].adjustedQuantity - 1);
  };

  if (items.length === 0) {
    return (
      <div className="text-center py-4">
        <IonText>
          <p className="text-sm text-neutral-500">Aucun équipement à valider</p>
        </IonText>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {items.map((item, index) => (
        <div
          key={`${item.type}-${index}`}
          className="bg-white border border-gray-200 rounded-lg p-3 shadow-sm"
        >
          <div className="flex items-center justify-between">
            {/* Section gauche : Checkbox + Info */}
            <div className="flex items-center gap-3 flex-1">
              <input
                type="checkbox"
                onChange={(e) => handleCheckboxChange(index, e.target.checked)}
                checked={item.isValidated}
                className="h-5 w-5 primary-color border-gray-300 rounded "
              />

              <div className="flex-1">
                <span className="font-semibold text-gray-800 text-sm">
                  {getEquipmentDisplayName(item.type)}
                </span>
              </div>
            </div>

            {/* Section droite : Contrôles de quantité */}
            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={() => decrementQuantity(index)}
                disabled={item.adjustedQuantity <= 0}
                className="w-6 h-6 flex items-center justify-center bg-gray-100 rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <IonIcon icon={remove} className="text-gray-600" />
              </button>

              <div className="w-12 text-center">
                {/*<span className="text-sm font-medium text-gray-800">{item.adjustedQuantity}</span>*/}
                <input type={"number"} className={ "w-full text-center text-sm font-medium text-gray-800 border border-gray-300 rounded p-1"}
                        value={item.adjustedQuantity}
                        onChange={(e) => handleQuantityChange(index, parseInt(e.target.value, 10))}
                        min={0}
                />

              </div>

              <button
                type="button"
                onClick={() => incrementQuantity(index)}
                className="w-6 h-6 flex items-center justify-center bg-gray-100 rounded-full"
              >
                <IonIcon icon={add} className="text-gray-600" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default EquipmentValidator;
