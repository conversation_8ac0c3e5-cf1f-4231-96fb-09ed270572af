import { IonAccordion, IonAccordionGroup, IonItem } from '@ionic/react';
import React, { useMemo } from 'react';

import { IStopEntity } from '../../../interfaces/entity/i-stop-entity';
import { EquipmentItem } from '../../services/deliverer/EquipmentValidator';
import { formatAddress } from '../../services/utils';
import TruncatedText from '../stylized/TruncatedText';

export interface StopCardProps {
  stop: IStopEntity;
  equipmentItems: EquipmentItem[];
  onToggleValidation: (isChecked: boolean) => void;
  children: React.ReactNode; // Equipment validation section
  className?: string;
}

export const StopCard: React.FC<StopCardProps> = ({
  stop,
  equipmentItems,
  onToggleValidation,
  children,
  className = '',
}) => {
  const clientName = stop.originalClientInfo?.name || 'Client inconnu';
  const address = formatAddress(stop.originalClientInfo?.address);

  const totalWeight = useMemo(() => (
    (stop.shipmentLines || []).reduce((acc, line) => acc + (Number(line.weightKg) || 0), 0)
  ), [stop.shipmentLines]);
  const totalEquipmentCount = useMemo(() => (
    equipmentItems?.reduce((acc, item) => acc + item.adjustedQuantity, 0) || 0
  ), [equipmentItems]);
  const isValidated = useMemo(() => (
    equipmentItems.every((item) => item.isValidated)
  ), [equipmentItems]);

  return (
    <div
      className={`mb-4 p-1 bg-white rounded-lg shadow-sm border border-neutral-200 flex flex-row-reverse ${className}`}
    >
      <IonAccordionGroup className="rounded-lg flex flex-1" value={stop.id}>
        <IonAccordion value={stop.id}>
          <IonItem slot="header" className="bg-gradient-to-r from-blue-50 to-blue-100">
            <div className="flex  justify-between mr-3 w-full py-2">
              <div className="flex-1 flex flex-col gap-1">
                <TruncatedText className="primary-color" text={clientName} maxLength={20} />
                <TruncatedText className="text-neutral-500 text-xs" text={address} maxLength={35} />
                <p className="text-sm font-semibold text-neutral-700">
                  Poids : {totalWeight.toFixed(2)} Kg
                </p>
              </div>
              <div className=" flex items-start gap-2">
                <span className={'font-semibold text-neutral-700 mr-2'}>
                  Total: {totalEquipmentCount}
                </span>
              </div>
            </div>
          </IonItem>

          <div className="bg-white p-2 flex flex-col gap-4" slot="content">
            {children}
          </div>
        </IonAccordion>
      </IonAccordionGroup>

      <div className="flex items-start pl-4 pt-8">
        <input
          type="checkbox"
          checked={isValidated}
          onChange={(e) => onToggleValidation(e.target.checked)}
          className="h-5 w-5 primary-color border-gray-300 rounded"
        />
      </div>
    </div>
  );
};
