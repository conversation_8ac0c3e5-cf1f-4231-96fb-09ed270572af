import { IonAvatar, IonText } from '@ionic/react';
import React from 'react';

import { TourWithDriver } from '../../../hooks/useTourManagement';
import { TourStatus } from '../../../interfaces/enum/tour.enums';
import { formatDate } from '../../../utils/dateUtils';

export interface TourCardProps {
  tour: TourWithDriver;
  onClick: () => void;
  statusColor?: string;
  className?: string;
  showDeliveryDate?: boolean;
}

export const TourCard: React.FC<TourCardProps> = ({
  tour,
  onClick,
  statusColor = 'medium',
  className = '',
  showDeliveryDate = false,
}) => {
  const getBackgroundColor = () => {
    if (statusColor === 'success') return 'bg-green-50';
    if (statusColor === 'warning') return 'bg-yellow-50';
    return 'bg-gray-50';
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:shadow-md transition-shadow ${getBackgroundColor()} ${className}`}
      onClick={onClick}
    >
      <div className="flex flex-col gap-2 p-4">
        <p className="text-xl font-semibold primary-color">
          Tournée {tour.tourIdentifier.originalNumber}
        </p>
        <div className="flex flex-col gap-1 text-sm">
          <div className="font-medium flex items-center gap-2">
            <IonAvatar className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 flex-shrink-0">
              <img alt="Avatar" src="https://ionicframework.com/docs/img/demos/avatar.svg" />
            </IonAvatar>
            <IonText className="text-sm font-medium text-gray-900/80">{tour.driverName}</IonText>
          </div>
        </div>
        {(showDeliveryDate && tour.status === TourStatus.Completed && tour.deliveryDate) && (
          <IonText className="text-sm font-medium text-gray-900/60">
            Fait le {formatDate(tour.deliveryDate)}
          </IonText>
        )}
      </div>
    </div>
  );
};