/**
 * @file useStopOperations.ts
 * @description Hook personnalisé pour gérer les opérations sur les arrêts (preload, load, completion)
 * selon le flux défini dans flux-preload-load-completion.md
 */

import { useCallback, useMemo } from 'react';
import { ICompleteStopDeliveryDto } from '../interfaces/dto/stop-delivery.dto';
import { ILoadStopDto, IPreloadStopDto } from '../interfaces/dto/load-stop.dto';
import { addEventToQueue } from '../stores/eventQueueSlice';
import { makeGetStopByIdSelector } from '../stores/tourSlice';
import { useAppDispatch, useAppSelector } from '../utils/redux';

/**
 * Hook personnalisé pour gérer les opérations sur un arrêt
 * Implémente le pattern Event Sourcing avec queue d'événements
 * 
 * @param stopId - Identifiant de l'arrêt
 * @returns Objet contenant l'état de l'arrêt et les fonctions d'opération
 */
export function useStopOperations(stopId: string) {
  const dispatch = useAppDispatch();
  
  // Créer le sélecteur une seule fois
  const getStopById = useMemo(() => makeGetStopByIdSelector(), []);
  
  // Récupérer l'état de l'arrêt avec projection des événements
  const stop = useAppSelector(state => getStopById(state, stopId));

  /**
   * Précharge l'équipement pour un arrêt
   * Ajoute un événement 'stop-preload' à la queue
   */
  const preloadStop = useCallback((data: IPreloadStopDto) => {
    dispatch(addEventToQueue({
      eventType: 'stop-preload',
      payload: { ...data, stopId }
    }));
  }, [dispatch, stopId]);

  /**
   * Charge définitivement l'équipement et démarre la tournée
   * Ajoute un événement 'stop-load' à la queue
   */
  const loadStop = useCallback((data: ILoadStopDto) => {
    dispatch(addEventToQueue({
      eventType: 'stop-load',
      payload: { ...data, stopId }
    }));
  }, [dispatch, stopId]);

  /**
   * Complète la livraison avec preuves et détails
   * Ajoute un événement 'stop-completion' à la queue
   */
  const completeStop = useCallback((data: ICompleteStopDeliveryDto) => {
    dispatch(addEventToQueue({
      eventType: 'stop-completion',
      payload: { ...data, stopId }
    }));
  }, [dispatch, stopId]);

  return {
    stop,
    preloadStop,
    loadStop,
    completeStop
  };
}