/**
 * @file useTourLoadingState.ts
 * @description Hook pour gérer l'état de chargement des tournées et la validation des stops
 */

import { useCallback, useRef, useState } from "react";

import { EquipmentItem } from "../components/services/deliverer/EquipmentValidator";
import { ITourEntity } from "../interfaces/entity/i-tour-entity";
import { TourStatus } from "../interfaces/enum/tour.enums";
import { loadingControlPersistenceService } from "../services/LoadingControlPersistenceService";
import { extractEquipmentFromStop } from "../utils/equipment-extractor";
import { useAppSelector } from "../utils/redux";

/**
 * Hook pour gérer l'état de chargement des tournées
 */
export function useTourLoadingState() {
  const tours = useAppSelector((state) => state.tour.tours);
  const isLoading = useAppSelector((state) => !state.tour.isDataResolved);

  // États locaux pour la validation des stops
  const [stopValidationStates, setStopValidationStates] = useState<Record<string, boolean>>({});
  const [stopEquipmentStates, setStopEquipmentStates] = useState<Record<string, EquipmentItem[]>>(
    {},
  );
  const isInitializingRef = useRef(false);

  // Filtrage des tournées planifiées
  const plannedTours = tours.filter((tour) => tour.status === TourStatus.Planned);

  /**
   * Charge les validations et équipements depuis le cache pour une tournée
   */
  const loadValidationsForTour = useCallback(async (selectedTour: ITourEntity) => {
    if (!selectedTour) {
      return;
    }

    isInitializingRef.current = true;

    try {
      const tourValidations = await loadingControlPersistenceService.getStopValidationsForTour(
        selectedTour.id,
        'load',
      );

      const validationStates: Record<string, boolean> = {};
      const equipmentStates: Record<string, EquipmentItem[]> = {};

      selectedTour.stops?.forEach((stop) => {
        const stopId = stop.id;
        const cachedValidation = tourValidations[stopId];

        if (cachedValidation?.equipmentItems && cachedValidation.equipmentItems.length > 0) {
          equipmentStates[stopId] = cachedValidation.equipmentItems;
          // Recalculer la validation avec la logique correcte (au cas où l'ancienne logique était incorrecte)
          const itemsWithQuantity = cachedValidation.equipmentItems.filter(
            (item) => item.adjustedQuantity >= 0,
          );
          validationStates[stopId] =
            itemsWithQuantity.length > 0 && itemsWithQuantity.every((item) => item.isValidated);
        } else {
          const extractedItems = extractEquipmentFromStop(stop, selectedTour.tourIdentifier.type);
          equipmentStates[stopId] = extractedItems;
          validationStates[stopId] = false;
        }
      });

      setStopValidationStates(validationStates);
      setStopEquipmentStates(equipmentStates);
    } catch (error) {
      console.error('Erreur lors du chargement des validations load:', error);
    } finally {
      setTimeout(() => {
        isInitializingRef.current = false;
      }, 100);
    }
  }, []);

  /**
   * Gère la validation d'un arrêt
   */
  const handleStopValidation = useCallback(
    async (stopId: string, tourId: string, isValid: boolean) => {
      if (isInitializingRef.current) {
        return;
      }

      setStopValidationStates((prev) => {
        if (prev[stopId] === isValid) {
          return prev;
        }

        return { ...prev, [stopId]: isValid };
      });

      try {
        const equipmentItems = stopEquipmentStates[stopId] || [];
        await loadingControlPersistenceService.saveStopValidation(
          stopId,
          tourId,
          isValid,
          equipmentItems,
          'load',
        );
      } catch (error) {
        console.error('Erreur lors de la sauvegarde de la validation load:', error);
      }
    },
    [stopEquipmentStates],
  );

  /**
   * Gère les changements d'équipement pour un arrêt
   */
  const handleEquipmentChange = useCallback(
    async (stopId: string, tourId: string, equipmentItems: EquipmentItem[]) => {
      if (isInitializingRef.current) {
        return;
      }

      const currentItems = stopEquipmentStates[stopId] || [];

      if (JSON.stringify(currentItems) === JSON.stringify(equipmentItems)) {
        return;
      }

      setStopEquipmentStates((prev) => ({
        ...prev,
        [stopId]: equipmentItems,
      }));

      // Un stop est validé seulement si TOUS les équipements avec quantité > 0 sont validés
      const itemsWithQuantity = equipmentItems.filter((item) => item.adjustedQuantity > 0);
      const hasValidatedItems =
        itemsWithQuantity.length > 0 && itemsWithQuantity.every((item) => item.isValidated);

      setStopValidationStates((prev) => ({
        ...prev,
        [stopId]: hasValidatedItems,
      }));

      try {
        await loadingControlPersistenceService.saveStopValidation(
          stopId,
          tourId,
          hasValidatedItems,
          equipmentItems,
          'load',
        );
      } catch (error) {
        console.error('Erreur lors de la sauvegarde des équipements load:', error);
      }
    },
    [stopEquipmentStates],
  );

  /**
   * Valide ou dévalide tous les équipements d'un arrêt
   */
  const handleToggleAllEquipments = useCallback(
    (stopId: string, tourId: string, isChecked: boolean) => {
      const currentItems = stopEquipmentStates[stopId] || [];
      const updatedItems = currentItems.map((item) => ({
        ...item,
        isValidated: isChecked,
      }));

      handleEquipmentChange(stopId, tourId, updatedItems);
    },
    [stopEquipmentStates, handleEquipmentChange],
  );

  /**
   * Vérifie si tous les stops d'une tournée sont validés
   */
  const areAllStopsValidatedForTour = useCallback(
    (tour: ITourEntity) => {
      const tourStops = tour.stops || [];

      return tourStops.length > 0 && tourStops.every((stop) => stopValidationStates[stop.id]);
    },
    [stopValidationStates],
  );

  /**
   * Vérifie si tous les stops de toutes les tournées sont validés
   */
  const areAllStopsValidatedGlobally = useCallback(() => {
    const allStops = plannedTours.flatMap((tour) => tour.stops || []);

    return allStops.length > 0 && allStops.every((stop) => stopValidationStates[stop.id]);
  }, [plannedTours, stopValidationStates]);

  return {
    // États
    isLoading,
    plannedTours,
    stopValidationStates,
    stopEquipmentStates,

    // Fonctions
    loadValidationsForTour,
    handleStopValidation,
    handleEquipmentChange,
    handleToggleAllEquipments,
    areAllStopsValidatedForTour,
    areAllStopsValidatedGlobally,
  };
}
