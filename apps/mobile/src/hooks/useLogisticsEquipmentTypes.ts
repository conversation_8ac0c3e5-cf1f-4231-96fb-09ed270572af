/**
 * @file useLogisticsEquipmentTypes.ts
 * @description Hook pour gérer les types d'équipement logistique
 */

import { useEffect, useState } from 'react';
import { ILogisticsEquipmentTypeEntity } from '../interfaces/entity/i-logistics-equipment-type-entity';
import { LogisticsEquipmentKind } from '../interfaces/enum/logistics-equipment-kind.enum';
import { logisticsEquipmentTypeService } from "../services";

/**
 * Hook pour récupérer et gérer les types d'équipement logistique
 */
export function useLogisticsEquipmentTypes() {
  const [equipmentTypes, setEquipmentTypes] = useState<ILogisticsEquipmentTypeEntity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEquipmentTypes = async () => {
      try {
        setLoading(true);
        setError(null);
        const types = await logisticsEquipmentTypeService.findAllForDeliver();
        console.log('🔍 [useLogisticsEquipmentTypes] Loaded equipment types:', {
          totalTypes: types.length,
          types: types.map(type => ({
            id: type.id,
            name: type.name,
            kind: type.kind
          }))
        });
        setEquipmentTypes(types);
      } catch (err) {
        console.error('Erreur lors du chargement des types d\'équipement:', err);
        setError('Impossible de charger les types d\'équipement logistique');
      } finally {
        setLoading(false);
      }
    };

    fetchEquipmentTypes();
  }, []);

  /**
   * Récupère les types d'équipement par kind
   */
  const getTypesByKind = (kind: LogisticsEquipmentKind): ILogisticsEquipmentTypeEntity[] => {
    return equipmentTypes.filter(type => type.kind === kind);
  };

  /**
   * Récupère un type d'équipement par son ID
   */
  const getTypeById = (id: string): ILogisticsEquipmentTypeEntity | undefined => {
    return equipmentTypes.find(type => type.id === id);
  };

  /**
   * Récupère le premier type d'équipement d'un kind donné
   * Utile pour les cas où on a besoin d'un type par défaut
   */
  const getDefaultTypeForKind = (kind: LogisticsEquipmentKind): ILogisticsEquipmentTypeEntity | undefined => {
    const types = getTypesByKind(kind);
    return types.length > 0 ? types[0] : undefined;
  };

  return {
    equipmentTypes,
    loading,
    error,
    getTypesByKind,
    getTypeById,
    getDefaultTypeForKind,
  };
}