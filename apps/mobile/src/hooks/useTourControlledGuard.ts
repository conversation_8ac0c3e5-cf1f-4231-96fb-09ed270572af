import { useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { PATHS } from '../config/routes';
import { TourStatus } from '../interfaces/enum/tour.enums';
import { getToursWithQueueProjection } from '../stores/tourSlice';
import { useAppSelector } from '../utils/redux';

/**
 * Hook qui vérifie si le contrôle de chargement a été effectué et redirige si nécessaire
 *
 * @param enabled - Si false, désactive la vérification (par défaut: true)
 * @returns isLoadingControlDone - true si le contrôle est terminé
 */
export const useTourControlledGuard = (enabled: boolean = true) => {
  const history = useHistory();
  const tours = useAppSelector(getToursWithQueueProjection);
  const isTourLoading = useAppSelector((state) => !state.tour.isDataResolved);

  const isLoadingControlDone =
    tours.length > 0 && tours.every((tour) => tour.status === TourStatus.InProgress);

  // Vérifier si le contrôle de chargement a été récemment effectué
  const isLoadingControlRecentlyCompleted = () => {
    const completed = localStorage.getItem('loadingControlCompleted');
    if (!completed) return false;
    
    const completedTime = parseInt(completed);
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000; // 5 minutes en millisecondes
    
    return (now - completedTime) < fiveMinutes;
  };

  useEffect(() => {
    if (!enabled || isTourLoading) {
      return;
    }

    if (tours.length === 0) {
      return;
    }

    // Ne pas rediriger si le contrôle de chargement a été récemment effectué
    if (!isLoadingControlDone && !isLoadingControlRecentlyCompleted()) {
      // Contrôle pas encore fait, rediriger vers la page de contrôle de chargement
      history.replace(PATHS.LOADING_CONTROL);

      return;
    }
  }, [enabled, tours, history, isLoadingControlDone, isTourLoading]);

  return {
    isLoadingControlDone,
    isTourLoading,
    tours,
  };
};
