import { useCallback, useRef, useState } from 'react';

import { EquipmentItem } from '../components/services/deliverer/EquipmentValidator';
import { IStopEntity } from '../interfaces/entity/i-stop-entity';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { loadingControlPersistenceService } from '../services/LoadingControlPersistenceService';
import { extractEquipmentFromCompletedStop } from '../utils/equipment-extractor';

export interface UseTourValidationStateProps {
  workflow: 'preload' | 'load' | 'return';
}

export const useTourValidationState = ({ workflow }: UseTourValidationStateProps) => {
  const [stopValidationStates, setStopValidationStates] = useState<Record<string, boolean>>({});
  const [stopEquipmentStates, setStopEquipmentStates] = useState<Record<string, EquipmentItem[]>>(
    {},
  );
  const isInitializingRef = useRef(false);

  const loadValidationsForTour = useCallback(
    async (tour: ITourEntity) => {
      if (!tour.stops) {
        return;
      }

      isInitializingRef.current = true;

      try {
        const tourValidations = await loadingControlPersistenceService.getStopValidationsForTour(
          tour.id,
          workflow,
        );
        const validationStates: Record<string, boolean> = {};
        const equipmentStates: Record<string, EquipmentItem[]> = {};

        tour.stops.forEach((stop) => {
          const stopId = stop.id;
          const cachedValidation = tourValidations[stopId];

          if (cachedValidation?.equipmentItems && cachedValidation.equipmentItems.length > 0) {
            equipmentStates[stopId] = cachedValidation.equipmentItems;
            validationStates[stopId] = cachedValidation.isValidated;
          } else {
            equipmentStates[stopId] = extractEquipmentFromCompletedStop(
              stop,
              tour.tourIdentifier.type,
            );
            validationStates[stopId] = false;
          }
        });

        setStopValidationStates(validationStates);
        setStopEquipmentStates(equipmentStates);
      } catch (error) {
        console.error(`Erreur lors du chargement des validations ${workflow}:`, error);
      } finally {
        setTimeout(() => {
          isInitializingRef.current = false;
        }, 100);
      }
    },
    [workflow],
  );

  const handleStopValidation = useCallback(
    async (stopId: string, tourId: string, isValid: boolean) => {
      if (isInitializingRef.current) {
        return;
      }

      setStopValidationStates((prev) => {
        if (prev[stopId] === isValid) {
          return prev;
        }

        return {
          ...prev,
          [stopId]: isValid,
        };
      });

      try {
        const equipmentItems = stopEquipmentStates[stopId] || [];
        await loadingControlPersistenceService.saveStopValidation(
          stopId,
          tourId,
          isValid,
          equipmentItems,
          workflow,
        );
      } catch (error) {
        console.error(`Erreur lors de la sauvegarde de la validation ${workflow}:`, error);
      }
    },
    [stopEquipmentStates, workflow],
  );

  const handleEquipmentChange = useCallback(
    async (stopId: string, tourId: string, equipmentItems: EquipmentItem[]) => {
      if (isInitializingRef.current) {
        return;
      }

      const currentItems = stopEquipmentStates[stopId] || [];

      if (JSON.stringify(currentItems) === JSON.stringify(equipmentItems)) {
        return;
      }

      setStopEquipmentStates((prev) => ({
        ...prev,
        [stopId]: equipmentItems,
      }));

      const hasValidatedItems = equipmentItems.some((item) => item.isValidated);

      setStopValidationStates((prev) => ({
        ...prev,
        [stopId]: hasValidatedItems,
      }));

      try {
        await loadingControlPersistenceService.saveStopValidation(
          stopId,
          tourId,
          hasValidatedItems,
          equipmentItems,
          workflow,
        );
      } catch (error) {
        console.error(`Erreur lors de la sauvegarde des équipements ${workflow}:`, error);
      }
    },
    [stopEquipmentStates, workflow],
  );

  const handleToggleAllEquipments = useCallback(
    (stopId: string, tourId: string, isChecked: boolean) => {
      const currentItems = stopEquipmentStates[stopId] || [];
      const updatedItems = currentItems.map((item) => ({
        ...item,
        isValidated: isChecked,
      }));

      handleEquipmentChange(stopId, tourId, updatedItems);
    },
    [stopEquipmentStates, handleEquipmentChange],
  );

  const areAllStopsValidatedForTour = useCallback(
    (tour: ITourEntity): boolean => {
      if (!tour.stops) {
        return false;
      }

      return tour.stops.every((stop) => stopValidationStates[stop.id] === true);
    },
    [stopValidationStates],
  );

  const calculateStopTotalWeight = useCallback((stop: IStopEntity): number => {
    let totalWeight = 0;

    if (stop.shipmentLines && stop.shipmentLines.length > 0) {
      stop.shipmentLines.forEach((shipmentLine) => {
        totalWeight += Number(shipmentLine.weightKg) || 0;
      });
    }

    return totalWeight;
  }, []);

  const getTotalEquipmentCount = useCallback((equipmentItems: EquipmentItem[]): number => {
    return equipmentItems.reduce((acc, item) => acc + item.adjustedQuantity, 0) || 0;
  }, []);

  const resetValidationsForTour = useCallback(
    async (tourId: string) => {
      try {
        await loadingControlPersistenceService.resetStopValidationsForTour(tourId, workflow);

        // Reset local states
        setStopValidationStates({});
        setStopEquipmentStates({});
      } catch (error) {
        console.error(`Erreur lors du reset des validations ${workflow}:`, error);
      }
    },
    [workflow],
  );

  return {
    stopValidationStates,
    stopEquipmentStates,
    loadValidationsForTour,
    handleStopValidation,
    handleEquipmentChange,
    handleToggleAllEquipments,
    areAllStopsValidatedForTour,
    calculateStopTotalWeight,
    getTotalEquipmentCount,
    resetValidationsForTour,
  };
};
