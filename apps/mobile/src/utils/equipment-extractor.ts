import { IStopEntity } from '../interfaces/entity/i-stop-entity';
import { LogisticsEquipmentKind } from '../interfaces/enum/logistics-equipment-kind.enum';
import { EquipmentItem } from '../components/services/deliverer/EquipmentValidator';
import { TourType } from '../interfaces/enum/tour.enums';

/**
 * Extrait les données d'équipement depuis les shipmentLines d'un stop
 */
export const extractEquipmentFromStop = (stop: IStopEntity, tourType?: TourType): EquipmentItem[] => {

  // Initialiser toutes les catégories de base à 0
  const equipmentMap = new Map<LogisticsEquipmentKind, number>([
    [LogisticsEquipmentKind.PALLET, 0],
    [LogisticsEquipmentKind.ROLL, 0],
    [LogisticsEquipmentKind.PACKAGE, 0],
  ]);



  // Agréger les quantités par type d'équipement depuis les shipmentLines filtrées
  stop.shipmentLines?.forEach((shipmentLine) => {
    if (shipmentLine.palletCount) {
      const current = equipmentMap.get(LogisticsEquipmentKind.PALLET) || 0;
      equipmentMap.set(LogisticsEquipmentKind.PALLET, current + shipmentLine.palletCount);
    }

    if (shipmentLine.rollCount) {
      const current = equipmentMap.get(LogisticsEquipmentKind.ROLL) || 0;
      equipmentMap.set(LogisticsEquipmentKind.ROLL, current + shipmentLine.rollCount);
    }

    if (shipmentLine.packageCount) {
      const current = equipmentMap.get(LogisticsEquipmentKind.PACKAGE) || 0;
      equipmentMap.set(LogisticsEquipmentKind.PACKAGE, current + shipmentLine.packageCount);
    }
  });

  // Convertir en tableau d'EquipmentItem (toujours afficher toutes les catégories)
  const equipmentItems: EquipmentItem[] = [];

  equipmentMap.forEach((quantity, type) => {
    equipmentItems.push({
      type,
      receivedQuantity: quantity,
      adjustedQuantity: quantity, // Par défaut, la quantité ajustée = quantité reçue
      isValidated: false, // Par défaut, non validé
    });
  });



  return equipmentItems;
};


export const extractEquipmentFromCompletedStop = (stop: IStopEntity, tourType?: TourType): EquipmentItem[] => {

  // Initialiser toutes les catégories de base à 0
  const equipmentMap = new Map<LogisticsEquipmentKind, number>([
    [LogisticsEquipmentKind.PALLET, 0],
    [LogisticsEquipmentKind.ROLL, 0],
    [LogisticsEquipmentKind.PACKAGE, 0],
  ]);



  // Agréger les quantités par type d'équipement depuis les shipmentLines filtrées

 if (stop?.completion?.returnedEquipmentCount && stop.completion.returnedEquipmentCount) {
    if (stop.completion.returnedEquipmentCount.palletCount) {
      const current = equipmentMap.get(LogisticsEquipmentKind.PALLET) || 0;
      equipmentMap.set(LogisticsEquipmentKind.PALLET, current + stop.completion.returnedEquipmentCount.palletCount);
    }

    if (stop.completion.returnedEquipmentCount.rollCount) {
      const current = equipmentMap.get(LogisticsEquipmentKind.ROLL) || 0;
      equipmentMap.set(LogisticsEquipmentKind.ROLL, current + stop.completion.returnedEquipmentCount.rollCount);
    }

    if (stop.completion.returnedEquipmentCount.packageCount) {
      const current = equipmentMap.get(LogisticsEquipmentKind.PACKAGE) || 0;
      equipmentMap.set(LogisticsEquipmentKind.PACKAGE, current + stop.completion.returnedEquipmentCount.packageCount);
    }
 }
  // Convertir en tableau d'EquipmentItem (toujours afficher toutes les catégories)
  const equipmentItems: EquipmentItem[] = [];

  equipmentMap.forEach((quantity, type) => {
    equipmentItems.push({
      type,
      receivedQuantity: quantity,
      adjustedQuantity: quantity, // Par défaut, la quantité ajustée = quantité reçue
      isValidated: false, // Par défaut, non validé
    });
  });



  return equipmentItems;
};