import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { keyBy } from 'lodash';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';

interface ReceptionistPreloadInterface {
  // key is tourIdentifier.originalNumber
  tourList: Record<string, ITourEntity>;
}

const initialState: ReceptionistPreloadInterface = {
  tourList: {},
};

export const receptionistPreloadSlice = createSlice({
  name: 'receptionistPreload',
  initialState,
  reducers: {
    setTourList: (state, action: PayloadAction<ITourEntity[]>) => {
      state.tourList = keyBy(action.payload, 'tourIdentifier.originalNumber');
    },
  },
});

export const { setTourList: setReceptionistPreloadTourList } = receptionistPreloadSlice.actions;

export default receptionistPreloadSlice.reducer;
