import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { IPreloadStopDto } from '../interfaces/dto/load-stop.dto';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';

type StopId = string;

interface ReceptionistPreloadInterface {
  tours: ITourEntity[];
  searchTerm: string;
  preloadStopDtos: Record<StopId, IPreloadStopDto>;
}

const initialState: ReceptionistPreloadInterface = {
  tours: [],
  searchTerm: '',
  preloadStopDtos: {},
};

export const receptionistPreloadSlice = createSlice({
  name: 'receptionistPreload',
  initialState,
  reducers: {
    setTours: (state, action: PayloadAction<ITourEntity[]>) => {
      state.tours = action.payload ?? [];
    },
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.searchTerm = action.payload;
    },
    removeTour: (state, action: PayloadAction<string>) => {
      state.tours = state.tours.filter((tour) => tour.id !== action.payload);
    },
    clearSearchTerm: (state) => {
      state.searchTerm = '';
    },
    addPreloadStopDto: (
      state,
      action: PayloadAction<{ stopId: string; preloadStopDto: IPreloadStopDto }>,
    ) => {
      state.preloadStopDtos[action.payload.stopId] = action.payload.preloadStopDto;
    },
    updatePreloadStopDto: (
      state,
      action: PayloadAction<{ stopId: string; preloadStopDto: IPreloadStopDto }>,
    ) => {
      state.preloadStopDtos[action.payload.stopId] = action.payload.preloadStopDto;
    },
  },
});

// Sélecteurs
const selectReceptionistPreload = (state: any) => state.receptionistPreload;

// Sélecteur pour les tournées filtrées par recherche
export const selectFilteredTours = createSelector([selectReceptionistPreload], (preload) => {
  let tours = preload.tours;

  // Filtrer par terme de recherche
  if (preload.searchTerm.trim()) {
    const searchLower = preload.searchTerm.toLowerCase().trim();
    tours = tours.filter((tour: ITourEntity) => {
      const tourOriginalNumber = tour.tourIdentifier.originalNumber?.toLowerCase() || '';

      return tourOriginalNumber.includes(searchLower);
    });
  }

  return tours;
});

export const {
  setTours: setReceptionistPreloadTour,
  setSearchTerm,
  removeTour,
  clearSearchTerm,
  addPreloadStopDto,
  updatePreloadStopDto,
} = receptionistPreloadSlice.actions;

export default receptionistPreloadSlice.reducer;
