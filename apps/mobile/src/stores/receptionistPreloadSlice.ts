import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { TourStatus } from '../interfaces/enum/tour.enums';

export interface TourWithDriver extends ITourEntity {
  driverName: string;
  driverFirstName: string;
  driverLastName: string;
}

interface ReceptionistPreloadInterface {
  // Liste des tournées avec informations chauffeur
  tours: TourWithDriver[];
  // Terme de recherche
  searchTerm: string;
  // État de chargement
  isLoading: boolean;
  // Erreur éventuelle
  error: string | null;
  // Segment actuel (preload ou return)
  currentSegment: 'preload' | 'return';
}

const initialState: ReceptionistPreloadInterface = {
  tours: [],
  searchTerm: '',
  isLoading: false,
  error: null,
  currentSegment: 'preload',
};

export const receptionistPreloadSlice = createSlice({
  name: 'receptionistPreload',
  initialState,
  reducers: {
    setTours: (state, action: PayloadAction<TourWithDriver[]>) => {
      state.tours = action.payload;
      state.isLoading = false;
      state.error = null;
    },
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.searchTerm = action.payload;
    },
    setCurrentSegment: (state, action: PayloadAction<'preload' | 'return'>) => {
      state.currentSegment = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    removeTour: (state, action: PayloadAction<string>) => {
      state.tours = state.tours.filter((tour) => tour.id !== action.payload);
    },
    clearSearchTerm: (state) => {
      state.searchTerm = '';
    },
  },
});

// Sélecteurs
const selectReceptionistPreload = (state: any) => state.receptionistPreload;

// Sélecteur pour les tournées de départ (à pré-charger)
export const selectDepartureTours = createSelector([selectReceptionistPreload], (preload) =>
  preload.tours.filter(
    (tour: TourWithDriver) =>
      tour.status === TourStatus.Planned || tour.status === TourStatus.InProgress,
  ),
);

// Sélecteur pour les tournées de retour (terminées)
export const selectReturnTours = createSelector([selectReceptionistPreload], (preload) =>
  preload.tours.filter((tour: TourWithDriver) => tour.status === TourStatus.Completed),
);

// Sélecteur pour les tournées du segment actuel
export const selectCurrentSegmentTours = createSelector(
  [selectReceptionistPreload, selectDepartureTours, selectReturnTours],
  (preload, departureTours, returnTours) => {
    return preload.currentSegment === 'preload' ? departureTours : returnTours;
  },
);

// Sélecteur pour les tournées filtrées par recherche
export const selectFilteredTours = createSelector(
  [selectCurrentSegmentTours, selectReceptionistPreload],
  (currentTours, preload) => {
    if (!preload.searchTerm.trim()) {
      return currentTours;
    }

    const searchLower = preload.searchTerm.toLowerCase().trim();

    return currentTours.filter((tour: TourWithDriver) => {
      const tourOriginalNumber = tour.tourIdentifier.originalNumber?.toLowerCase() || '';
      const driverName = tour.driverName?.toLowerCase() || '';

      return tourOriginalNumber.includes(searchLower) || driverName.includes(searchLower);
    });
  },
);

export const {
  setTours,
  setSearchTerm,
  setCurrentSegment,
  setLoading,
  setError,
  removeTour,
  clearSearchTerm,
} = receptionistPreloadSlice.actions;

export default receptionistPreloadSlice.reducer;
