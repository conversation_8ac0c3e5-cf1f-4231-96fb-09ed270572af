import { createAsyncThunk, createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { cloneDeep } from 'lodash';
import { DateTime } from 'luxon';
import { ILoadStopDto } from '../interfaces/dto/load-stop.dto';
import { ICompleteStopDeliveryDto } from '../interfaces/dto/stop-delivery.dto';
import { IStopCompletion } from '../interfaces/entity/i-stop-completion-entity';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { DeliveryStatus } from '../interfaces/enum/stop-completion.enum';
import { UserRole } from '../interfaces/enum/user-role.enum';
import { tourReceptionistService } from '../services';
import { tourDeliverService } from '../services/TourDeliverService';
import { indexedDBPreferencesStorage } from '../utils/indexeddb-preferences-storage';
import { queueDeliveryNotesForDownload } from './deliveryNotesSlice';
import { RootState } from './store';

export interface TourState {
  tours: ITourEntity[];
  loading: boolean;
  error: string | null;
  isDataResolved: boolean;
  dataResolvedAt: string | null;
}

const initialState: TourState = {
  tours: [],
  loading: false,
  error: null,
  isDataResolved: false,
  dataResolvedAt: null,
};

export const getToursForToday = createAsyncThunk(
  'tour/getToursForToday',
  async (
    {
      hasInternet,
      userRole,
    }: { hasInternet: boolean; userRole: UserRole.RECEPTIONIST | UserRole.DELIVERER },
    { dispatch },
  ) => {
    const todayDate = DateTime.now().toISODate();

    if (hasInternet) {
      try {
        let fromApi: ITourEntity[];

        if (userRole === UserRole.RECEPTIONIST) {
          fromApi = await tourReceptionistService.getTodayTours();
        } else {
          fromApi = await tourDeliverService.getTodayTours();
        }

        const dataResolvedAt = new Date().toISOString();

        await indexedDBPreferencesStorage.setItem(
          `tours`,
          JSON.stringify({
            date: todayDate,
            tours: fromApi,
            dataResolvedAt,
          }),
        );

        // Queue delivery notes for download
        if (userRole === UserRole.DELIVERER) {
          for (const tour of fromApi) {
            if (tour.stops && tour.stops.length > 0) {
              const deliveryNotesWithContext = tour.stops.flatMap((stop) =>
                (stop.deliveryNotes || []).map((note) => ({
                  ...note,
                  tourId: tour.id,
                  stopId: stop.id,
                })),
              );
              dispatch(queueDeliveryNotesForDownload(deliveryNotesWithContext));
            }
          }
        }

        return {
          tours: fromApi,
          dataResolvedAt,
        };
      } catch (error) {
        console.error('Error fetching tours from API:', error);
        // En cas d'erreur, essayer de récupérer depuis le cache
        const toursStringifiedFromStorage = await indexedDBPreferencesStorage.getItem(`tours`);

        if (toursStringifiedFromStorage) {
          const tours = JSON.parse(toursStringifiedFromStorage);

          if (tours.date === todayDate) {
            return {
              tours: tours.tours,
              dataResolvedAt: tours.dataResolvedAt,
            };
          }
        }
        throw error;
      }
    }

    const toursStringifiedFromStorage = await indexedDBPreferencesStorage.getItem(`tours`);

    if (!toursStringifiedFromStorage) {
      return null;
    }

    const tours = JSON.parse(toursStringifiedFromStorage);

    if (tours.date !== todayDate) {
      return null;
    }

    return {
      tours: tours.tours,
      dataResolvedAt: tours.dataResolvedAt,
    };
  },
);

export const clearAllTourCache = createAsyncThunk(
  'tour/clearAllTourCache',
  async (_, { dispatch: _dispatch }) => {
    try {
      // Vider le cache IndexedDB des tours
      await indexedDBPreferencesStorage.removeItem('tours');

      // Vider aussi les autres caches si nécessaire
      console.log('Cache tours vidé avec succès');

      return true;
    } catch (error) {
      console.error('Erreur lors du vidage du cache:', error);
      throw error;
    }
  },
);

const tourSlice = createSlice({
  name: 'tour',
  initialState,
  reducers: {
    setTours: (state, action: PayloadAction<ITourEntity[]>) => {
      state.tours = action.payload;
      state.loading = false;
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.loading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetTourState: (state) => {
      state.tours = [];
      state.loading = false;
      state.error = null;
      state.isDataResolved = false;
      state.dataResolvedAt = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getToursForToday.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getToursForToday.fulfilled, (state, action) => {
        state.tours = action.payload?.tours ?? [];

        if (action.payload) {
          state.isDataResolved = true;
          state.dataResolvedAt = action.payload.dataResolvedAt;
        }

        state.loading = false;
        state.error = null;
      })
      .addCase(getToursForToday.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to load tours';
      })
      .addCase(clearAllTourCache.pending, (state) => {
        state.loading = true;
      })
      .addCase(clearAllTourCache.fulfilled, (state) => {
        state.tours = [];
        state.loading = false;
        state.error = null;
        state.isDataResolved = false;
        state.dataResolvedAt = null;
      })
      .addCase(clearAllTourCache.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to clear cache';
      });
  },
});

export const { setTours, setLoading, setError, clearError, resetTourState } = tourSlice.actions;

export default tourSlice.reducer;

/** Un peu de la même façon que pour de l'event sourcing, on va projeter letat des tours en prennant compte des events de la queue */
export const getToursWithQueueProjection = createSelector(
  (state: RootState) => state.tour.tours,
  (state: RootState) => state.eventQueue.queue,
  (state: RootState) => state.tour.dataResolvedAt,
  (_tours, queue, dataResolvedAt) => {
    const tours = cloneDeep(_tours) as ITourEntity[];

    if (!dataResolvedAt) {
      return tours;
    }

    return tours.map((tour) => {
      tour.stops = tour.stops?.map((stop) => {
        const eventsForThisStop = queue.filter(
          (event) => (event.payload as any)?.stopId === stop.id,
        );

        if (!eventsForThisStop?.length) {
          return stop;
        }

        eventsForThisStop.forEach((event) => {
          if (event.createdAt < dataResolvedAt) {
            return;
          }

          const eventType = event.eventType;

          if (eventType === 'stop-completion') {
            const payload = event.payload as ICompleteStopDeliveryDto;

            if (!stop.completion) {
              stop.completion = {
                deliveryStatus: payload.deliveryCompletionType as unknown as DeliveryStatus,
              } as IStopCompletion;
            }

            stop.completion.deliveryStatus =
              payload.deliveryCompletionType as unknown as DeliveryStatus;
          }

          if (eventType === 'stop-load' || eventType === 'stop-preload') {
            const payload = event.payload as ILoadStopDto;

            if (!stop.completion) {
              stop.completion = {
                deliveryStatus: DeliveryStatus.PENDING,
              } as IStopCompletion;
            }

            if (eventType === 'stop-load') {
              stop.completion.loadedEquipmentCount = payload.equipmentCount;
            } else {
              stop.completion.preloadedEquipmentCount = payload.equipmentCount;
            }
          }
        });

        return stop;
      });

      return tour;
    });
  },
);

/** Factory function to create a selector that gets a stop by its id */
export const makeGetStopByIdSelector = () =>
  createSelector([getToursWithQueueProjection, (state, stopId) => stopId], (tours, stopId) => {
    return tours.flatMap((tour) => tour.stops || []).find((s) => s.id === stopId);
  });
