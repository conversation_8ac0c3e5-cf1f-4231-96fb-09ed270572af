import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { IPaginatedResponse, IPaginationParams } from '../interfaces/pagination';
import { apiService } from './ApiService';

export class TourManagerService {
  private readonly endpoint = '/api/receptionist/tours';

  /**
   * Récupère les tours par date
   */
  async getToursByDate(
    date: string,
    params?: IPaginationParams,
  ): Promise<IPaginatedResponse<ITourEntity>> {
    return apiService.getPaginated<ITourEntity>(`${this.endpoint}/by-date/${date}`, params);
  }

  /**
   * Récupère les tours d'aujourd'hui
   */
  async getTodayTours(params?: IPaginationParams): Promise<IPaginatedResponse<ITourEntity>> {
    const response = await apiService.getPaginated<ITourEntity>(`${this.endpoint}/today`, params);
    console.log('response', response);
    return response;
  }
}

export const tourManagerService = new TourManagerService();
