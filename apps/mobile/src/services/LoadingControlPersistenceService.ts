import { EquipmentQuantityData } from '../components/services/deliverer/delivery-process/types';
import { EquipmentItem } from '../components/services/deliverer/EquipmentValidator';
import { capacitorPreferencesStorage } from '../utils/capacitor-preferences-storage';

export interface TourValidationState {
  tourId: string;
  isCompletelyValidated: boolean;
  isPartiallyValidated: boolean; // Au moins une checkbox cochée
  equipmentData: EquipmentQuantityData | null;
  lastUpdated: number;
  validatedAt?: string; // Pour compatibilité
}

export interface StopValidationState {
  stopId: string;
  tourId: string;
  isValidated: boolean;
  equipmentData: EquipmentQuantityData | null;
  validatedEquipments: Record<string, boolean>; // equipmentTypeId -> isValidated
  equipmentItems?: EquipmentItem[]; // Nouveau format simple
  lastUpdated: string;
  workflowType?: 'preload' | 'load'; // NOUVEAU: Type de workflow
}

export interface LoadingControlState {
  validations: Record<string, TourValidationState>;
  stopValidations: Record<string, StopValidationState>; // stopId -> validation state
  lastUpdated: string;
}

// Type de workflow pour distinguer preload vs load
export type WorkflowType = 'preload' | 'load';

class LoadingControlPersistenceService {
  private readonly STORAGE_KEY = 'loadingControl_validations';
  private readonly STOP_VALIDATIONS_KEY = 'loadingControl_stopValidations';
  private readonly PRELOAD_STOP_VALIDATIONS_KEY = 'preload_stopValidations';
  private readonly LOAD_STOP_VALIDATIONS_KEY = 'load_stopValidations';

  /**
   * Obtenir la clé de storage selon le type de workflow
   */
  private getStorageKeyForWorkflow(workflowType: WorkflowType): string {
    return workflowType === 'preload'
      ? this.PRELOAD_STOP_VALIDATIONS_KEY
      : this.LOAD_STOP_VALIDATIONS_KEY;
  }

  /**
   * Sauvegarder l'état de validation d'un stop avec le nouveau format
   */
  async saveStopValidation(
    stopId: string,
    tourId: string,
    isValidated: boolean,
    equipmentItems: EquipmentItem[],
    workflowType: WorkflowType = 'load', // Par défaut load pour compatibilité
  ): Promise<void> {
    try {
      console.log('💾 [LoadingControlPersistence] Saving stop validation:', {
        stopId,
        tourId,
        isValidated,
        equipmentItems,
        workflowType,
      });

      const storageKey = this.getStorageKeyForWorkflow(workflowType);
      const currentValidations = await this.getStopValidationsForWorkflow(workflowType);

      currentValidations[stopId] = {
        stopId,
        tourId,
        isValidated,
        equipmentData: null,
        validatedEquipments: {},
        equipmentItems,
        lastUpdated: new Date().toISOString(),
        workflowType,
      };

      await capacitorPreferencesStorage.setItem(storageKey, JSON.stringify(currentValidations));

      console.log(
        '✅ [LoadingControlPersistence] Stop validation saved successfully for',
        workflowType,
      );
    } catch (error) {
      console.error('❌ [LoadingControlPersistence] Error saving stop validation:', error);
      throw error;
    }
  }

  /**
   * Récupérer les validations de stops pour un workflow spécifique
   */
  async getStopValidationsForWorkflow(
    workflowType: WorkflowType,
  ): Promise<Record<string, StopValidationState>> {
    try {
      const storageKey = this.getStorageKeyForWorkflow(workflowType);
      const data = await capacitorPreferencesStorage.getItem(storageKey);

      if (!data) {
        return {};
      }

      return JSON.parse(data) as Record<string, StopValidationState>;
    } catch (error) {
      console.error(
        '❌ [LoadingControlPersistence] Error getting stop validations for',
        workflowType,
        ':',
        error,
      );

      return {};
    }
  }

  /**
   * Récupérer les validations de stops pour une tournée avec type de workflow
   */
  async getStopValidationsForTour(
    tourId: string,
    workflowType: WorkflowType = 'load',
  ): Promise<Record<string, StopValidationState>> {
    try {
      const allValidations = await this.getStopValidationsForWorkflow(workflowType);
      const tourValidations: Record<string, StopValidationState> = {};

      Object.entries(allValidations).forEach(([stopId, validation]) => {
        if (validation.tourId === tourId) {
          tourValidations[stopId] = validation;
        }
      });

      console.log('📖 [LoadingControlPersistence] Tour validations for', workflowType, ':', {
        tourId,
        validationCount: Object.keys(tourValidations).length,
        stopIds: Object.keys(tourValidations),
      });

      return tourValidations;
    } catch (error) {
      console.error(
        '❌ [LoadingControlPersistence] Error getting tour validations for',
        workflowType,
        ':',
        error,
      );

      return {};
    }
  }

  /**
   * Reset du cache pour un workflow spécifique
   */
  async resetStopValidationsForWorkflow(workflowType: WorkflowType): Promise<void> {
    try {
      const storageKey = this.getStorageKeyForWorkflow(workflowType);
      await capacitorPreferencesStorage.removeItem(storageKey);

      console.log('🧹 [LoadingControlPersistence] Reset cache for', workflowType);
    } catch (error) {
      console.error(
        '❌ [LoadingControlPersistence] Error resetting cache for',
        workflowType,
        ':',
        error,
      );
      throw error;
    }
  }

  /**
   * Reset du cache pour une tournée spécifique dans un workflow
   */
  async resetStopValidationsForTour(tourId: string, workflowType: WorkflowType): Promise<void> {
    try {
      const allValidations = await this.getStopValidationsForWorkflow(workflowType);

      // Supprimer toutes les validations pour cette tournée
      Object.keys(allValidations).forEach((stopId) => {
        if (allValidations[stopId].tourId === tourId) {
          delete allValidations[stopId];
        }
      });

      const storageKey = this.getStorageKeyForWorkflow(workflowType);
      await capacitorPreferencesStorage.setItem(storageKey, JSON.stringify(allValidations));

      console.log(
        '🧹 [LoadingControlPersistence] Reset cache for tour',
        tourId,
        'in',
        workflowType,
      );
    } catch (error) {
      console.error(
        '❌ [LoadingControlPersistence] Error resetting tour cache for',
        workflowType,
        ':',
        error,
      );
      throw error;
    }
  }

  /**
   * Vérifier si tous les stops d'une tournée sont validés
   */
  async areAllStopsValidatedForTour(tourId: string, stopIds: string[]): Promise<boolean> {
    try {
      const tourValidations = await this.getStopValidationsForTour(tourId);

      return stopIds.every((stopId) => tourValidations[stopId]?.isValidated === true);
    } catch (error) {
      console.error('❌ [LoadingControlPersistence] Error checking tour stops validation:', error);

      return false;
    }
  }

  /**
   * Vider les validations de stops (nouveau jour ou reset)
   */
  async clearAllStopValidations(): Promise<void> {
    try {
      console.log('🗑️ [LoadingControlPersistence] Clearing all stop validations');
      const currentState = await this.getLoadingControlState();
      currentState.stopValidations = {};
      currentState.lastUpdated = new Date().toISOString();

      await capacitorPreferencesStorage.setItem(this.STORAGE_KEY, JSON.stringify(currentState));
      console.log('✅ [LoadingControlPersistence] All stop validations cleared');
    } catch (error) {
      console.error('❌ [LoadingControlPersistence] Error clearing stop validations:', error);
      throw error;
    }
  }

  /**
   * Sauvegarder l'état de validation d'une tournée
   */
  async saveTourValidation(tourId: string, validationState: TourValidationState): Promise<void> {
    try {
      console.log('💾 [LoadingControlPersistence] Saving tour validation:', {
        tourId,
        validationState,
      });

      const currentState = await this.getLoadingControlState();
      console.log('💾 [LoadingControlPersistence] Current state before save:', currentState);

      // S'assurer que validations existe
      if (!currentState.validations) {
        console.log('💾 [LoadingControlPersistence] Initializing validations object');
        currentState.validations = {};
      }

      currentState.validations[tourId] = {
        ...validationState,
        validatedAt: new Date().toISOString(),
      };
      currentState.lastUpdated = new Date().toISOString();

      console.log('💾 [LoadingControlPersistence] State after update:', currentState);

      await capacitorPreferencesStorage.setItem(this.STORAGE_KEY, JSON.stringify(currentState));

      console.log('✅ [LoadingControlPersistence] Tour validation saved successfully');
    } catch (error) {
      console.error('❌ [LoadingControlPersistence] Error saving tour validation:', error);
      throw error;
    }
  }

  /**
   * Récupérer l'état de validation d'une tournée
   */
  async getTourValidation(tourId: string): Promise<TourValidationState | null> {
    try {
      const state = await this.getLoadingControlState();

      return state.validations[tourId] || null;
    } catch (error) {
      console.error('❌ [LoadingControlPersistence] Error getting tour validation:', error);

      return null;
    }
  }

  /**
   * Récupérer tous les états de validation
   */
  async getAllValidations(): Promise<Record<string, TourValidationState>> {
    try {
      const state = await this.getLoadingControlState();
      console.log('📖 [LoadingControlPersistence] Raw state:', state);
      console.log('📖 [LoadingControlPersistence] Validations:', state.validations);

      // S'assurer qu'on retourne toujours un objet, jamais undefined
      const validations = state.validations || {};
      console.log('📖 [LoadingControlPersistence] Final validations returned:', validations);

      return validations;
    } catch (error) {
      console.error('❌ [LoadingControlPersistence] Error getting all validations:', error);

      // Retourner un objet vide au lieu d'undefined
      return {};
    }
  }

  /**
   * Vérifier si toutes les tournées sont validées
   */
  async areAllToursValidated(tourIds: string[]): Promise<boolean> {
    try {
      const validations = await this.getAllValidations();

      return tourIds.every((tourId) => validations[tourId]?.isCompletelyValidated === true);
    } catch (error) {
      console.error('❌ [LoadingControlPersistence] Error checking all tours validation:', error);

      return false;
    }
  }

  /**
   * Vider les validations (nouveau jour ou reset)
   */
  async clearAllValidations(): Promise<void> {
    try {
      console.log('🗑️ [LoadingControlPersistence] Clearing all validations');
      await capacitorPreferencesStorage.removeItem(this.STORAGE_KEY);
      console.log('✅ [LoadingControlPersistence] All validations cleared');
    } catch (error) {
      console.error('❌ [LoadingControlPersistence] Error clearing validations:', error);
      throw error;
    }
  }

  /**
   * Obtenir un résumé des validations
   */
  async getValidationSummary(): Promise<{
    totalTours: number;
    validatedTours: number;
    remainingTours: number;
    completionPercentage: number;
  }> {
    try {
      const validations = await this.getAllValidations();
      const tourIds = Object.keys(validations);
      const validatedCount = Object.values(validations).filter(
        (v) => v.isCompletelyValidated,
      ).length;

      return {
        totalTours: tourIds.length,
        validatedTours: validatedCount,
        remainingTours: tourIds.length - validatedCount,
        completionPercentage: tourIds.length > 0 ? (validatedCount / tourIds.length) * 100 : 0,
      };
    } catch (error) {
      console.error('❌ [LoadingControlPersistence] Error getting validation summary:', error);

      return {
        totalTours: 0,
        validatedTours: 0,
        remainingTours: 0,
        completionPercentage: 0,
      };
    }
  }

  /**
   * Vérifier si les validations sont d'aujourd'hui (optionnel)
   */
  async isValidationFromToday(): Promise<boolean> {
    try {
      const state = await this.getLoadingControlState();

      if (!state.lastUpdated) {
        return false;
      }

      const lastUpdate = new Date(state.lastUpdated);
      const today = new Date();

      return (
        lastUpdate.getDate() === today.getDate() &&
        lastUpdate.getMonth() === today.getMonth() &&
        lastUpdate.getFullYear() === today.getFullYear()
      );
    } catch (error) {
      console.error('❌ [LoadingControlPersistence] Error checking validation date:', error);

      return false;
    }
  }

  /**
   * Récupérer l'état complet du contrôle de chargement
   */
  private async getLoadingControlState(): Promise<LoadingControlState> {
    try {
      const stored = await capacitorPreferencesStorage.getItem(this.STORAGE_KEY);

      if (stored) {
        console.log('📖 [LoadingControlPersistence] Found stored state:', stored);
        // S'assurer que validations existe même dans les données stockées
        const state = JSON.parse(stored) as LoadingControlState;

        if (!state.validations) {
          console.log('📖 [LoadingControlPersistence] Fixing missing validations in stored state');
          state.validations = {};
        }

        if (!state.stopValidations) {
          console.log(
            '📖 [LoadingControlPersistence] Fixing missing stopValidations in stored state',
          );
          state.stopValidations = {};
        }

        return state;
      }

      // État initial
      console.log('📖 [LoadingControlPersistence] Creating initial state');

      return {
        validations: {},
        stopValidations: {},
        lastUpdated: new Date().toISOString(),
      };
    } catch (error) {
      console.error('❌ [LoadingControlPersistence] Error getting loading control state:', error);

      // État de fallback
      return {
        validations: {},
        stopValidations: {},
        lastUpdated: new Date().toISOString(),
      };
    }
  }
}

export const loadingControlPersistenceService = new LoadingControlPersistenceService();
