import { ILoadStopDto, IPreloadStopDto } from '../interfaces/dto/load-stop.dto';
import { ICompleteStopDeliveryDto } from '../interfaces/dto/stop-delivery.dto';
import { IStopEntity } from '../interfaces/entity/i-stop-entity';
import { apiService } from './ApiService';

export class StopDeliverService {
  private readonly endpoint = '/api/deliver/stops';

  /**
   * Marquer un stop comme complété avec les détails de livraison
   */
  async completeStopDelivery(stopId: string, data: ICompleteStopDeliveryDto): Promise<IStopEntity> {
    return apiService.post<IStopEntity>(`${this.endpoint}/${stopId}/complete`, data);
  }

  async loadStop(stopId: string, data: ILoadStopDto): Promise<IStopEntity> {
    console.log('🔍 [StopDeliverService.loadStop] Sending data:', {
      stopId,
      data: JSON.stringify(data, null, 2)
    });
    return apiService.post<IStopEntity>(`${this.endpoint}/${stopId}/load`, data);
  }

  async preloadStop(stopId: string, data: IPreloadStopDto): Promise<IStopEntity> {
    console.log('🔍 [StopDeliverService.preloadStop] Sending data:', {
      stopId,
      endpoint: `${this.endpoint}/${stopId}/preload`,
      data: JSON.stringify(data, null, 2)
    });
    
    try {
      const result = await apiService.post<IStopEntity>(`${this.endpoint}/${stopId}/preload`, data);
      console.log('✅ [StopDeliverService.preloadStop] Success:', result);
      return result;
    } catch (error) {
      console.error('❌ [StopDeliverService.preloadStop] Error:', error);
      throw error;
    }
  }
}

export const stopDeliverService = new StopDeliverService();
