import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { apiService } from './ApiService';

export class TourDeliverService {
  private readonly endpoint = '/api/deliver/tours';

  /**
   * Récupérer les tournées du jour pour le livreur
   */
  async getTodayTours(): Promise<ITourEntity[]> {
    return apiService.get<ITourEntity[]>(`${this.endpoint}/today`);
  }

  /**
   * Télécharger une note de livraison
   */
  async downloadDeliveryNote(tourId: string, deliveryNoteId: string): Promise<Blob> {
    return apiService.getBlob(
      `${this.endpoint}/${tourId}/delivery-notes/${deliveryNoteId}/download`,
    );
  }
}

export const tourDeliverService = new TourDeliverService();
