import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, setupIonicReact } from '@ionic/react';

/* Core CSS required for Ionic components to work properly */
import '@ionic/react/css/core.css';

/* Basic CSS for apps built with Ionic */
import '@ionic/react/css/normalize.css';
import '@ionic/react/css/structure.css';
import '@ionic/react/css/typography.css';

/* Optional CSS utils that can be commented out */
import '@ionic/react/css/display.css';
import '@ionic/react/css/flex-utils.css';
import '@ionic/react/css/float-elements.css';
import '@ionic/react/css/padding.css';
import '@ionic/react/css/text-alignment.css';
import '@ionic/react/css/text-transformation.css';

/**
 * Ionic Dark Mode - DISABLED
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 *
 * Application fixed to LIGHT mode only
 */

/* import '@ionic/react/css/palettes/dark.always.css'; */
/* import '@ionic/react/css/palettes/dark.class.css'; */
/* import '@ionic/react/css/palettes/dark.system.css'; */

/* Route Configuration */
import { PATHS } from './config/routes';

/* Components */
import { IonReactRouter } from '@ionic/react-router';
import { Suspense, useEffect } from 'react';
import { Redirect, Route } from 'react-router-dom';
import { Layout } from './components/layout/Layout';
import Tabs from './components/layout/Tabs';
import { ApplicationStateStatus } from './components/services/app/ApplicationStateStatus';
import { Login } from './pages/auth/Login';
import { ApplicationStateProvider } from './plugin/applicationState/applicationStateProvider';
import { EventQueueSynchronization } from './plugin/applicationState/eventQueueSynchronisation';

import { TourSynchronization } from './plugin/applicationState/TourSynchronization';
import { AuthContextProvider } from './plugin/keycloak/context';

// Loading component
const PageLoader = () => (
  <div className="flex items-center justify-center h-screen">
    <IonSpinner name="crescent" />
  </div>
);

// Force light mode configuration
const forceLightMode = () => {
  // Remove any dark class from body
  document.body.classList.remove('dark');

  // Set light theme attribute
  document.body.setAttribute('data-theme', 'light');

  // Force light color scheme
  document.documentElement.style.colorScheme = 'light';
};

setupIonicReact({
  mode: 'md', // 'md' ou 'ios'
  animated: true,
  swipeBackEnabled: true,
});

export const App: React.FC = () => {
  // Force light mode on app startup
  useEffect(() => {
    forceLightMode();
  }, []);

  return (
    <ApplicationStateProvider>
      <IonApp>
        <IonReactRouter>
          <AuthContextProvider>
            <EventQueueSynchronization />
            <TourSynchronization />
            <IonRouterOutlet id="main">
              <Suspense fallback={<PageLoader />}>
                <Route exact path={PATHS.LOGIN} component={Login} />

                <Route path={PATHS.ADMIN}>
                  <Layout>
                    <Tabs />
                  </Layout>
                </Route>

                <Route exact path="/">
                  <Redirect to={PATHS.LOGIN} />
                </Route>
              </Suspense>
            </IonRouterOutlet>
          </AuthContextProvider>
        </IonReactRouter>
      </IonApp>
      <ApplicationStateStatus />
    </ApplicationStateProvider>
  );
};
