import { LogisticsEquipmentKind } from '../interfaces/enum/logistics-equipment-kind.enum';

export const LOGISTICS_EQUIPMENT_DISPLAY_NAMES = {
  [LogisticsEquipmentKind.PACKAGE]: 'CAISSES',
  [LogisticsEquipmentKind.CARTON]: 'CARTONS',
  [LogisticsEquipmentKind.PALLET]: 'PALETTES',
  [LogisticsEquipmentKind.ROLL]: 'ROLLS',
  [LogisticsEquipmentKind.CHEP]: 'CHEP',
} as const;

export const getLogisticsEquipmentDisplayName = (kind: LogisticsEquipmentKind): string => {
  return LOGISTICS_EQUIPMENT_DISPLAY_NAMES[kind];
};
