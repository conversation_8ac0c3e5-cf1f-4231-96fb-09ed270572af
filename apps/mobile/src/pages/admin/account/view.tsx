import {
  IonButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonList,
} from '@ionic/react';
import {
  cameraOutline,
  mailOutline,
  personOutline,
} from 'ionicons/icons';
import { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { getRouteTitle } from '../../../config/routes';
import { useAppSelector } from '../../../utils/redux';

const Profile: React.FC = () => {
  const location = useLocation();
  const pageTitle = getRouteTitle(location.pathname);
  const currentUser = useAppSelector((state) => state.currentUser.user);
  const [editMode, setEditMode] = useState(false);

  // Informations de profil modifiables (exemple)
  const [profileData, setProfileData] = useState({
    firstName: currentUser?.firstName || '',
    lastName: currentUser?.lastName || '',
    email: currentUser?.email || '',
  });

  return (
    <div
      style={{ paddingTop: '60px', paddingBottom: '60px' }}
      className="ion-padding bg-gray-50 h-full overflow-auto"
    >
      <div className="max-w-4xl mx-auto pt-4">
        <div className="my-4">
          <h1 className="text-4xl font-bold mb-4">{pageTitle}</h1>
          <p className="text-xl text-neutral-600 dark:text-neutral-300">
            Manage your personal information and account settings
          </p>
        </div>

        {/* Profile Header Card */}
        <div className="flex flex-col md:flex-row items-center mb-6 bg-white rounded-xl shadow-md p-6">
          <div className="relative">
            <div className="w-24 h-24 rounded-full bg-blue-100 flex items-center justify-center mb-4 md:mb-0 md:mr-6">
              <span className="text-3xl font-bold text-blue-600">
                {currentUser
                  ? `${currentUser?.firstName?.[0] || ''}${currentUser?.lastName?.[0] || ''}`
                  : '--'}
              </span>
            </div>
            <div className="absolute bottom-0 right-0 bg-blue-500 rounded-full p-1">
              <IonIcon icon={cameraOutline} className="text-white text-sm" />
            </div>
          </div>

          <div className="flex-1 text-center md:text-left">
            <h2 className="text-2xl font-bold text-gray-800">
              {currentUser
                ? `${currentUser?.firstName || '--'} ${currentUser?.lastName || '--'}`
                : '--'}
            </h2>
          </div>

          <div className="mt-4 md:mt-0">
            <IonButton fill="outline" color="primary" onClick={() => setEditMode(!editMode)}>
              {editMode ? 'Cancel' : 'Edit Profile'}
            </IonButton>
          </div>
        </div>

        {/* Personal Information */}
        <IonCard className="shadow-md mb-6">
          <IonCardHeader className="flex items-center">
            <IonIcon size="large" icon={personOutline} className="text-blue-600 mr-2" />
            <h3 className="font-semibold text-gray-700">Personal Information</h3>
          </IonCardHeader>
          <IonCardContent>
            <IonList className="bg-transparent">
              <IonItem lines="full" className="mb-2">
                <IonIcon slot="start" icon={personOutline} className="text-gray-500" />
                <IonLabel position="stacked">First Name</IonLabel>
                <IonInput
                  value={profileData.firstName || ''}
                  disabled={!editMode}
                  onIonChange={(e) =>
                    setProfileData({
                      ...profileData,
                      firstName: e.detail.value || '',
                    })
                  }
                  placeholder="--"
                />
              </IonItem>

              <IonItem lines="full" className="mb-2">
                <IonIcon slot="start" icon={personOutline} className="text-gray-500" />
                <IonLabel position="stacked">Last Name</IonLabel>
                <IonInput
                  value={profileData.lastName || ''}
                  disabled={!editMode}
                  onIonChange={(e) =>
                    setProfileData({
                      ...profileData,
                      lastName: e.detail.value || '',
                    })
                  }
                  placeholder="--"
                />
              </IonItem>

              <IonItem lines="full" className="mb-2">
                <IonIcon slot="start" icon={mailOutline} className="text-gray-500" />
                <IonLabel position="stacked">Email</IonLabel>
                <IonInput
                  value={profileData.email || ''}
                  disabled={!editMode}
                  onIonChange={(e) =>
                    setProfileData({
                      ...profileData,
                      email: e.detail.value || '',
                    })
                  }
                  placeholder="--"
                />
              </IonItem>
            </IonList>

            {editMode && (
              <div className="flex justify-end mt-4">
                <IonButton color="primary">Save Changes</IonButton>
              </div>
            )}
          </IonCardContent>
        </IonCard>

      </div>
    </div>
  );
};

export default Profile;
