/**
 * @file LoadingControl.tsx
 * @description Composant de contrôle de chargement des tournées pour les livreurs.
 *
 * Refactorisé pour implémenter le flux preload-load-completion selon
 * la documentation flux-preload-load-completion.md
 *
 * Permet aux livreurs de :
 * - Visualiser les tournées plannifiées par type (surgelé/frais)
 * - Valider les équipements (palettes, rolls, colis) pour chaque arrêt
 * - Naviguer entre les pages de tournées avec validation obligatoire
 * - Utiliser la queue d'événements pour les opérations hors ligne
 *
 * <AUTHOR> Team
 */

import {
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonLabel,
  IonPage,
  IonSegment,
  IonSegmentButton,
  IonText,
  IonTitle,
  IonToolbar,
} from '@ionic/react';
import { ArrowLeft, CheckCircle } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useHistory } from 'react-router';

import { EquipmentValidationSection } from '../../../../components/services/deliverer/loading-control/EquipmentValidationSection';
import InlineButtons from '../../../../components/ui/stylized/InlineButtons';
import { PATHS } from '../../../../config/routes';
import { TourType } from '../../../../interfaces/enum/tour.enums';
import { useTourLoadingState } from '../../../../hooks/useTourLoadingState';
import { useTourLoadOperations } from '../../../../hooks/useTourLoadOperations';
import { useTourValidationState } from '../../../../hooks/useTourValidationState';
import { useTourManagement } from '../../../../hooks/useTourManagement';
import { ITourEntity } from '../../../../interfaces/entity/i-tour-entity';

/**
 * Type pour les pages de tournées
 */
type PageType = 'ramasse' | 'frozen' | 'normal';

/**
 * Composant principal de contrôle de chargement des tournées
 *
 * Refactorisé pour utiliser les nouveaux hooks et composants.
 * Implémente le flux load selon la documentation.
 */
const LoadingControl: React.FC = () => {
  const history = useHistory();

  // Legacy hooks for compatibility
  const { isLoading, plannedTours, areAllStopsValidatedGlobally } = useTourLoadingState();

  const { loadAllTours } = useTourLoadOperations();

  // New shared hooks
  const tourManagement = useTourManagement({ tours: plannedTours });
  const tourValidation = useTourValidationState({ workflow: 'load' });

  // États locaux pour la navigation
  const [currentPage, setCurrentPage] = useState<PageType>('ramasse');
  const [selectedTourId, setSelectedTourId] = useState<string>('');

  // Filtrage des tournées using shared hook
  const frozenTours = tourManagement.frozenTours;
  const normalTours = tourManagement.normalTours;
  const allTours = plannedTours; // Pour la page ramasse (tous les tours sans filtre)

  /**
   * Initialise la page par défaut en fonction des tournées disponibles
   * et nettoie le flag de contrôle si nécessaire
   */
  useEffect(() => {
    // Nettoyer le flag de contrôle de chargement si on a des tournées planifiées
    // (cela signifie qu'on doit refaire le contrôle)
    if (plannedTours.length > 0) {
      const today = new Date().toDateString();
      const lastCompletedDate = localStorage.getItem('loadingControlDate');
      
      if (lastCompletedDate !== today) {
        localStorage.removeItem('loadingControlCompleted');
        localStorage.removeItem('loadingControlDate');
      }
    }

    if (allTours.length > 0) {
      // Commencer par frozen s'il y en a, sinon par normal s'il y en a
      if (frozenTours.length > 0) {
        setCurrentPage('frozen');
      } else if (normalTours.length > 0) {
        setCurrentPage('normal');
      } else {
        setCurrentPage('ramasse');
      }
    }
  }, [allTours.length, frozenTours.length, normalTours.length, plannedTours.length]);

  /**
   * Sélectionne automatiquement la première tournée lors du changement de page
   */
  useEffect(() => {
    let toursForCurrentPage;

    if (currentPage === 'ramasse') {
      toursForCurrentPage = allTours;
    } else if (currentPage === 'frozen') {
      toursForCurrentPage = frozenTours;
    } else {
      toursForCurrentPage = normalTours;
    }

    if (toursForCurrentPage.length > 0) {
      const isCurrentTourInList = toursForCurrentPage.some((tour) => tour.id === selectedTourId);

      if (!isCurrentTourInList) {
        setSelectedTourId(toursForCurrentPage[0].id);
      }
    } else {
      setSelectedTourId('');
    }
  }, [currentPage, frozenTours, normalTours, allTours, selectedTourId]);

  /**
   * Charge les validations et équipements depuis le cache
   */
  useEffect(() => {
    const selectedTour = currentTours.find((tour) => tour.id === selectedTourId);

    if (selectedTour) {
      tourValidation.loadValidationsForTour(selectedTour);
    }
  }, [selectedTourId, tourValidation.loadValidationsForTour]);

  /**
   * Gestionnaire pour revenir à la page précédente
   */
  const handleGoBack = () => {
    history.push(PATHS.TOURS);
  };

  /**
   * Navigation vers la page suivante
   */
  const handleNextPage = () => {
    if (currentPage === 'frozen') {
      // Éviter d'aller sur la page frais si aucune tournée frais
      if (normalTours.length > 0) {
        setCurrentPage('normal');
      } else {
        setCurrentPage('ramasse');
      }
    } else if (currentPage === 'normal') {
      setCurrentPage('ramasse');
    }
  };

  /**
   * Navigation vers la page précédente
   */
  const handlePreviousPage = () => {
    if (currentPage === 'ramasse') {
      // Revenir à normal s'il y en a, sinon à frozen
      if (normalTours.length > 0) {
        setCurrentPage('normal');
      } else if (frozenTours.length > 0) {
        setCurrentPage('frozen');
      }
    } else if (currentPage === 'normal') {
      // Revenir à frozen s'il y en a, sinon sortir
      if (frozenTours.length > 0) {
        setCurrentPage('frozen');
      }
    } else if (currentPage === 'frozen') {
      // Première page, revenir en arrière (quitter)
      handleGoBack();
    }
  };

  /**
   * Gestionnaire pour charger toutes les tournées
   */
  const handleLoadAllTours = () => {
    loadAllTours(plannedTours);
  };

  // Calculs dérivés
  let currentTours: ITourEntity[];

  if (currentPage === 'ramasse') {
    currentTours = allTours;
  } else if (currentPage === 'frozen') {
    currentTours = frozenTours;
  } else {
    currentTours = normalTours;
  }

  const selectedTour = currentTours.find((tour) => tour.id === selectedTourId);
  const selectedTourStops = selectedTour ? selectedTour.stops || [] : [];

  // États dérivés pour la validation
  const areAllStopsValidatedGloballyValue = areAllStopsValidatedGlobally();
  const areAllStopsValidatedForCurrentTour = selectedTour
    ? tourValidation.areAllStopsValidatedForTour(selectedTour)
    : false;
  // Navigation logic
  const isOnRamassePage = currentPage === 'ramasse';
  const isOnFrozenPage = currentPage === 'frozen';
  const isOnLastPage = isOnRamassePage; // Ramasse est maintenant la dernière page
  const showNavigationButtons = !isOnLastPage;

  /**
   * Rend une carte d'arrêt avec accordéon
   */
  const renderStopCard = (stop: any) => {
    if (!selectedTour) {
      return null;
    }

    // Pour la page ramasse, utiliser un rendu différent (dummy)
    if (currentPage === 'ramasse') {
      return null; // TODO: Intégrer la logique de ramasse ici
      // return renderRamasseStopCard(stop);
    }

    return (
      <EquipmentValidationSection
        key={stop.id}
        stop={stop}
        equipmentItems={tourValidation.stopEquipmentStates[stop.id] || []}
        onEquipmentChange={(items) =>
          tourValidation.handleEquipmentChange(stop.id, selectedTour.id, items)
        }
        onValidationChange={(isValid) =>
          tourValidation.handleStopValidation(stop.id, selectedTour.id, isValid)
        }
        onToggleAllEquipments={(isChecked) =>
          tourValidation.handleToggleAllEquipments(stop.id, selectedTour.id, isChecked)
        }
      />
    );
  };

  // États de chargement
  if (isLoading) {
    return (
      <IonPage>
        <IonHeader>
          <IonToolbar>
            <IonButtons slot="start">
              <IonButton onClick={handleGoBack}>
                <ArrowLeft className="h-6 w-6" />
              </IonButton>
            </IonButtons>
            <IonTitle>Contrôle de chargement</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent fullscreen>
          <div className="bg-gray-50 h-full mx-1 overflow-auto pt-4 sm:pt-6 md:pt-8 lg:pt-10 pb-10 sm:pb-12 md:pb-14 lg:pb-16">
            <div className="max-w-7xl mx-auto pt-2 sm:pt-4 md:pt-6 flex flex-col h-full px-2 sm:px-4 md:px-6 lg:px-8">
              <div className="text-center mt-8">
                <IonText>
                  <p className="text-xl font-bold text-neutral-500">Chargement des tournées...</p>
                </IonText>
              </div>
            </div>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  // Aucune tournée disponible
  if (plannedTours.length === 0) {
    history.push(PATHS.TOURS);

    return (
      <IonPage>
        <IonHeader>
          <IonToolbar>
            <IonButtons slot="start">
              <IonButton onClick={handleGoBack}>
                <ArrowLeft className="h-6 w-6" />
              </IonButton>
            </IonButtons>
            <IonTitle>Contrôle de chargement</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent fullscreen>
          <div className="bg-gray-50 h-full mx-1 overflow-auto pt-4 sm:pt-6 md:pt-8 lg:pt-10 pb-10 sm:pb-12 md:pb-14 lg:pb-16">
            <div className="max-w-7xl mx-auto pt-2 sm:pt-4 md:pt-6 flex flex-col h-full px-2 sm:px-4 md:px-6 lg:px-8">
              <div className="text-center mt-8">
                <CheckCircle className="text-green-500 text-6xl mb-4 mx-auto" />
                <IonText>
                  <h2 className="text-xl font-bold text-neutral-700 mb-2">
                    Aucune tournée à charger
                  </h2>
                  <p className="text-neutral-500">
                    Toutes vos tournées sont déjà en cours ou terminées.
                  </p>
                </IonText>
                <div
                  onClick={() => history.push(PATHS.TOURS)}
                  className="primary-button mt-6 px-8 py-4 text-lg font-semibold rounded-full cursor-pointer inline-flex items-center justify-center"
                >
                  Voir mes tournées
                </div>
              </div>
            </div>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  // Interface principale
  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonButtons slot="start">
            <IonButton onClick={handleGoBack}>
              <ArrowLeft className="h-6 w-6" />
            </IonButton>
          </IonButtons>
          <IonTitle>Chargement des tournées</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen>
        <div className="bg-gray-50 h-full mx-1 overflow-auto pt-4 sm:pt-6 md:pt-8 lg:pt-10 pb-10 sm:pb-12 md:pb-14 lg:pb-16">
          <div className="max-w-7xl mx-auto pt-2 sm:pt-4 md:pt-6 flex flex-col h-full px-2 sm:px-4 md:px-6 lg:px-8">
            {/* En-tête avec informations */}
            <div className="my-2 sm:my-4 mb-6 sm:mb-8 md:mb-10 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-4">
              <div
                className="flex items-center gap-2 cursor-pointer"
                onClick={() => (isOnFrozenPage ? handleGoBack() : handlePreviousPage())}
              >
                <ArrowLeft className="h-6 w-6" />
                <p className="text-2xl font-bold text-neutral-700">
                  {currentPage === 'ramasse' && 'Chargement ramasse'}
                  {currentPage === 'frozen' && 'Chargement surgelés'}
                  {currentPage === 'normal' && 'Chargement frais'}
                </p>
              </div>
            </div>

            {/* Segments pour sélectionner les tournées */}
            {currentTours.length > 0 && currentPage !== "ramasse" && (
              <div className="mb-6">
                <IonSegment
                  value={selectedTourId}
                  onIonChange={(e) => setSelectedTourId(e.detail.value as string)}
                  className="bg-white rounded-lg shadow-sm h-16"
                  scrollable
                >
                  {currentTours.map((tour, index) => (
                    <IonSegmentButton
                      key={tour.id}
                      value={tour.id}
                      className={`border-neutral-200 border-r-2 border-l-2 `}
                    >
                      <IonLabel>N°{tour.tourIdentifier.originalNumber}</IonLabel>
                    </IonSegmentButton>
                  ))}
                </IonSegment>
              </div>
            )}

            {/* Liste des cartes de livraisons */}
            <div className="space-y-4 flex-1 pb-32">
              {currentPage === 'ramasse' ? (
                <div className="text-center py-16">
                  <div className="  border-gray-600 rounded-lg p-8 mx-4">
                    <IonText>
                      <h3 className="text-xl font-bold text-neutral-400 mb-2">
                        En construction
                      </h3>

                    </IonText>
                  </div>
                </div>
              ) : selectedTourStops.length > 0 ? (
                selectedTourStops.map(renderStopCard)
              ) : (
                <div className="text-center py-8">
                  <IonText>
                    <p className="text-lg text-neutral-500">
                      {selectedTour
                        ? 'Aucune livraison dans cette tournée'
                        : 'Sélectionnez une tournée pour voir les livraisons'}
                    </p>
                  </IonText>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Navigation et actions - Position fixe en bas */}
        <div className="fixed bottom-0 pt-6 pb-6 bg-white w-full left-0 right-0 z-10 space-y-4">
          {/* Boutons de navigation (pages intermédiaires) */}
          {showNavigationButtons && (
            <div className="max-w-7xl mx-4">
              <InlineButtons
                buttons={[
                  {
                    label: 'Suivant',
                    onClick: areAllStopsValidatedForCurrentTour ? handleNextPage : () => {},
                    classNames: {
                      button: `primary-button py-6 px-6 ${areAllStopsValidatedForCurrentTour ? '' : 'opacity-50'}`,
                      label: 'text-lg font-semibold',
                    },
                  },
                ]}
              />
            </div>
          )}

          {/* Bouton final : Commencer les livraisons (dernière page uniquement) */}
          {isOnLastPage && (
            <div className="max-w-7xl mx-4">
              <InlineButtons
                buttons={[
                  {
                    label: 'Commencer les tournées',
                    onClick: handleLoadAllTours,
                    disabled: !areAllStopsValidatedGloballyValue,
                    classNames: {
                      label: 'text-lg font-semibold',
                      button: areAllStopsValidatedGloballyValue
                        ? 'primary-button py-6 px-6'
                        : 'primary-button-outline opacity-50 py-6 px-6',
                    },
                  },
                ]}
              />
            </div>
          )}
        </div>
      </IonContent>
    </IonPage>
  );
};

export default LoadingControl;
