import {
  IonBackButton,
  IonBadge,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonChip,
  IonContent,
  IonHeader,
  IonIcon,
  IonLabel,
  IonPage,
  IonProgressBar,
  IonSpinner,
  IonText,
  IonTitle,
  IonToolbar,
} from '@ionic/react';
import { calendar, checkmarkCircle, document, navigate, time } from 'ionicons/icons';
import { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { useTourControlledGuard } from '../../../../../hooks/useTourControlledGuard';
import { ITourEntity } from '../../../../../interfaces/entity/i-tour-entity';
import { TourStatus } from '../../../../../interfaces/enum/tour.enums';
import { tourDeliverService } from '../../../../../services/TourDeliverService';
import { formatDateWithWeekday } from '../../../../../utils/dateUtils';
import StopItem from '../../../../components/services/tour/StopItem';

interface RouteParams {
  id: string;
}

const TourDetail: React.FC = () => {
  const { id } = useParams<RouteParams>();
  const [tour, setTour] = useState<ITourEntity | null>(null);
  const [loading, setLoading] = useState(true);
  useTourControlledGuard();

  useEffect(() => {
    loadTourDetail();
  }, [id]);

  const loadTourDetail = async () => {
    try {
      setLoading(true);
      // Pour l'instant, on récupère tous les tours du jour et on filtre
      // TODO: Ajouter un endpoint backend pour récupérer un tour spécifique
      const tours = await tourDeliverService.getTodayTours();
      const tourData = tours.find((t) => t.id === id);

      if (tourData) {
        setTour(tourData);
      } else {
        console.error('Tour not found');
      }
    } catch (error) {
      console.error('Error loading tour detail:', error);
    } finally {
      setLoading(false);
    }
  };

  const history = useHistory();
  const handleStopArrival = (stopId: string) => {
    // Navigate to stop delivery page
    history.push(`/admin/stop/${stopId}/delivery`);
  };

  const completedStops =
    tour?.stops?.filter((stop) => stop.completion?.completedAt != null).length || 0;
  const totalStops = tour?.stops?.length || 0;
  const progress = totalStops > 0 ? completedStops / totalStops : 0;

  if (loading || !tour) {
    return (
      <IonPage>
        <IonHeader>
          <IonToolbar>
            <IonButtons slot="start">
              <IonBackButton defaultHref="/tours" />
            </IonButtons>
            <IonTitle>Chargement...</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent className="ion-padding">
          <div className="flex flex-col items-center justify-center h-full">
            <IonSpinner name="crescent" className="w-12 h-12" />
            <IonText className="mt-4">Chargement de la tournée...</IonText>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/tours" />
          </IonButtons>
          <IonTitle>Tournée {tour.tourIdentifier?.originalNumber || tour.id.slice(0, 8)}</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen>
        {/* Progress bar */}
        <IonProgressBar value={progress} color="primary" />

        <div className="ion-padding">
          {/* En-tête amélioré de la tournée */}
          <IonCard className="mb-4">
            <IonCardContent>
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <IonText>
                    <h2 className="text-xl font-bold text-primary">
                      Tournée {tour.tourIdentifier?.originalNumber || tour.id.slice(0, 8)}
                    </h2>
                  </IonText>
                  <div className="flex items-center gap-2 mt-2">
                    <IonIcon icon={calendar} color="medium" size="small" />
                    <IonText color="medium" className="text-sm">
                      {formatDateWithWeekday(tour.deliveryDate)}
                    </IonText>
                  </div>
                </div>
                <IonChip
                  color={tour.status === TourStatus.Completed ? 'success' : 'warning'}
                  className="m-0 px-2"
                >
                  <IonIcon
                    icon={tour.status === TourStatus.Completed ? checkmarkCircle : time}
                    size="small"
                  />
                  <IonLabel className="font-medium px-1">{tour.status}</IonLabel>
                </IonChip>
              </div>

              {/* Statistiques */}
              <div className="grid grid-cols-3 gap-3 mt-4">
                <div className="bg-blue-50 rounded-lg p-3 text-center">
                  <IonIcon icon={navigate} color="primary" className="text-2xl" />
                  <IonText className="block mt-1">
                    <p className="text-lg font-bold">{totalStops}</p>
                    <p className="text-xs text-gray-600">Arrêts</p>
                  </IonText>
                </div>
                <div className="bg-green-50 rounded-lg p-3 text-center">
                  <IonIcon icon={checkmarkCircle} color="success" className="text-2xl" />
                  <IonText className="block mt-1">
                    <p className="text-lg font-bold">{completedStops}</p>
                    <p className="text-xs text-gray-600">Livrés</p>
                  </IonText>
                </div>
                <div className="bg-orange-50 rounded-lg p-3 text-center">
                  <IonIcon icon={time} color="warning" className="text-2xl" />
                  <IonText className="block mt-1">
                    <p className="text-lg font-bold">{totalStops - completedStops}</p>
                    <p className="text-xs text-gray-600">Restants</p>
                  </IonText>
                </div>
              </div>
            </IonCardContent>
          </IonCard>

          {/* Bouton feuille de route amélioré */}
          <IonButton expand="block" fill="outline" className="mb-4" color="primary">
            <IonIcon icon={document} slot="start" />
            CONSULTER LA FEUILLE DE ROUTE
          </IonButton>

          {/* Liste des arrêts avec le nouveau composant */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-3">
              <IonText>
                <h3 className="font-semibold text-lg">Arrêts de la tournée</h3>
              </IonText>
              <IonBadge color="primary" className="px-2">
                {tour.stops?.length || 0}
              </IonBadge>
            </div>

            {tour.stops?.map((stop, index) => {
              // const currentStopIndex =
              //   tour.stops?.findIndex((s) => !s.completion || s.completion.completedAt == null) ||
              //   0;
              // const isActive = index === currentStopIndex;

              return (
                <StopItem
                  key={stop.id}
                  stop={stop}
                  tourId={tour.id}
                  index={index}
                  isActive={true}
                  onArrival={() => handleStopArrival(stop.id)}
                />
              );
            })}
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default TourDetail;
