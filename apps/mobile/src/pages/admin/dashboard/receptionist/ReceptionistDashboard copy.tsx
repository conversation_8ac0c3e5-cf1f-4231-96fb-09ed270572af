import {
  IonButton,
  IonContent,
  IonLabel,
  IonModal,
  IonPage,
  IonSegment,
  IonSegmentButton,
  IonText,
  useIonToast,
} from '@ionic/react';
import { X } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

import { Loading } from '../../../../components/layout/Loading';
import { EquipmentValidationSection } from '../../../../components/services/deliverer/loading-control/EquipmentValidationSection';
import { SearchInput } from '../../../../components/ui/shared/SearchInput';
import { TourCard } from '../../../../components/ui/shared/TourCard';
import InlineButtons from '../../../../components/ui/stylized/InlineButtons';
import { TourWithDriver, useTourManagement } from '../../../../hooks/useTourManagement';
import { useTourPreloadOperations } from '../../../../hooks/useTourPreloadOperations';
import { useTourValidationState } from '../../../../hooks/useTourValidationState';
import { IStopEntity } from '../../../../interfaces/entity/i-stop-entity';
import { ITourEntity } from '../../../../interfaces/entity/i-tour-entity';
import { LogisticsEquipmentKind } from '../../../../interfaces/enum/logistics-equipment-kind.enum';
import { TourStatus } from '../../../../interfaces/enum/tour.enums';
import { tourAssignmentManagerService, tourReceptionistService } from '../../../../services';
import { formatDate } from '../../../../utils/dateUtils';
import { useAppSelector } from '../../../../utils/redux';

// État de validation par tournée (local)
interface TourValidationState {
  tourId: string;
  isCompletelyValidated: boolean; // Validation complète (toutes checkboxes + bouton validé)
  isPartiallyValidated: boolean; // Au moins une checkbox cochée
  equipmentData?: any; // Equipment data for backward compatibility
  lastUpdated: number;
  // Nouveau : distinguer le type de contrôle
  controlType: 'departure' | 'return'; // départ ou retour
  isReturnControlValidated: boolean; // Spécifique au contrôle de retour
}

// Type de contrôle
type ControlType = 'departure' | 'return';

const ReceptionistDashboard: React.FC = () => {
  const [presentToast] = useIonToast();
  const { preloadTour } = useTourPreloadOperations();

  const currentUser = useAppSelector((state) => state.currentUser.user);

  // États pour les tournées récupérées via les endpoints manager
  const [tours, setTours] = useState<TourWithDriver[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // État des validations par tournée
  const [tourValidations, setTourValidations] = useState<Record<string, TourValidationState>>({});

  // Tournée actuellement sélectionnée pour validation
  const [selectedTour, setSelectedTour] = useState<TourWithDriver | null>(null);

  // Nouveau : type de contrôle en cours
  const [_currentControlType, _setCurrentControlType] = useState<ControlType>('departure');

  // État du segment actuel (preload ou return)
  const [currentSegment, setCurrentSegment] = useState<'preload' | 'return'>('preload');

  // Use shared hooks
  const tourManagement = useTourManagement({ tours });
  const tourValidation = useTourValidationState({ workflow: 'preload' });

  const availableTours = tourManagement.availableToursForPreload;

  const departureTours = tourManagement.departureTours;
  const returnTours = tourManagement.returnTours;

  // Obtenir les tournées du segment actuel
  const currentSegmentTours = useMemo(() => {
    if (currentSegment === 'preload') {
      return departureTours; // Tournées planifiées pour pré-chargement
    } else {
      return returnTours; // Tournées terminées pour retour
    }
  }, [currentSegment, departureTours, returnTours]);

  const filteredCurrentSegmentTours = useMemo(() => {
    const toursToFilter = currentSegment === 'preload' ? departureTours : returnTours;

    if (!tourManagement.searchTerm.trim()) {
      return toursToFilter;
    }

    const searchLower = tourManagement.searchTerm.toLowerCase().trim();

    return toursToFilter.filter((tour) => {
      const tourOriginalNumber = tour.tourIdentifier.originalNumber?.toLowerCase() || '';
      const driverName = tour.driverName?.toLowerCase() || '';

      return tourOriginalNumber.includes(searchLower) || driverName.includes(searchLower);
    });
  }, [currentSegment, departureTours, returnTours, tourManagement.searchTerm]);

  // Charger toutes les tournées et les assignations via les endpoints manager
  useEffect(() => {
    const loadToursAndAssignments = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Récupérer toutes les tournées d'aujourd'hui ET toutes les assignations (augmenter la limite)
        const [toursArray, assignmentsResponse] = await Promise.all([
          tourReceptionistService.getTodayTours(),
          tourAssignmentManagerService.getTourAssignments({
            limit: 5000, // Augmenter la limite
          }),
        ]);

        // Créer une map des assignations pour un matching plus efficace
        // IMPORTANT: Tours have composite unique key: originalNumber + deliveryDate
        const assignmentMap = new Map();
        assignmentsResponse.items.forEach((assignment) => {
          const key = assignment.tourIdentifier.originalNumber;

          if (!assignmentMap.has(key)) {
            assignmentMap.set(key, []);
          }
          assignmentMap.get(key).push(assignment);
        });

        // Joindre les tournées avec les informations des chauffeurs
        const toursWithDrivers: TourWithDriver[] = [];

        toursArray.forEach((tour) => {
          // Chercher l'assignation correspondante
          const assignments = assignmentMap.get(tour.tourIdentifier.originalNumber) || [];

          // Filtrer les assignations pour cette date de livraison spécifique
          const validAssignments = assignments.filter(
            (assignment: { fromDate: any; toDate: any }) => {
              const assignmentFromDate = assignment.fromDate;
              const assignmentToDate = assignment.toDate;
              const tourDeliveryDate = tour.deliveryDate;

              // Vérifier si la date de livraison de la tournée est dans la plage de l'assignation
              if (assignmentToDate) {
                return (
                  tourDeliveryDate >= assignmentFromDate && tourDeliveryDate <= assignmentToDate
                );
              } else {
                // Assignation ouverte (pas de date de fin)
                return tourDeliveryDate >= assignmentFromDate;
              }
            },
          );

          if (validAssignments.length > 0) {
            // Prendre la première assignation valide (ou la plus récente)
            const assignment = validAssignments[0];
            const driverFirstName = assignment?.user?.firstName || '';
            const driverLastName = assignment?.user?.lastName || '';
            const driverName = `${driverFirstName} ${driverLastName}`.trim() || 'Non assigné';

            toursWithDrivers.push({
              ...tour,
              driverName,
              driverFirstName,
              driverLastName,
            });
          } else {
            // Ajouter quand même la tournée sans chauffeur pour debug
            toursWithDrivers.push({
              ...tour,
              driverName: 'Non assigné',
              driverFirstName: '',
              driverLastName: '',
            });
          }
        });

        setTours(toursWithDrivers);

        // Initialiser les validations
        const initialValidations: Record<string, TourValidationState> = {};

        toursWithDrivers.forEach((tour) => {
          const controlType = tour.status === TourStatus.Completed ? 'return' : 'departure';

          initialValidations[tour.id] = {
            tourId: tour.id,
            isCompletelyValidated: false,
            isPartiallyValidated: false,
            lastUpdated: 0,
            controlType,
            isReturnControlValidated: false,
          };
        });

        setTourValidations(initialValidations);
      } catch (error) {
        console.error('❌ [ReceptionistDashboard] Erreur lors du chargement:', error);
        setError('Erreur lors du chargement des tournées et assignations');
      } finally {
        setIsLoading(false);
      }
    };

    loadToursAndAssignments();
  }, []);

  // Calculer si tous les contrôles sont terminés
  const allToursValidated = useMemo(() => {
    return (
      availableTours.length > 0 &&
      availableTours.every((tour) => {
        const validation = tourValidations[tour.id];

        if (tour.status === TourStatus.Completed) {
          return validation?.isReturnControlValidated;
        }

        return validation?.isCompletelyValidated;
      })
    );
  }, [availableTours, tourValidations]);

  // Load validations when tour is selected
  useEffect(() => {
    if (selectedTour) {
      tourValidation.loadValidationsForTour(selectedTour);
    }
  }, [selectedTour, tourValidation.loadValidationsForTour]);

  const areAllStopsValidatedForSelectedTour = useMemo(() => {
    if (!selectedTour) {
      console.log('🔍 [areAllStopsValidatedForSelectedTour] No selected tour');

      return false;
    }

    const result = tourValidation.areAllStopsValidatedForTour(selectedTour);
    console.log('🔍 [areAllStopsValidatedForSelectedTour] Validation result:', {
      tourId: selectedTour.id,
      stopsCount: selectedTour.stops?.length,
      allValidated: result,
      stopValidationStates: tourValidation.stopValidationStates,
    });

    return result;
  }, [
    selectedTour,
    tourValidation.areAllStopsValidatedForTour,
    tourValidation.stopValidationStates,
  ]);

  const renderStopCard = (stop: IStopEntity) => {
    const items = tourValidation.stopEquipmentStates[stop.id] || [];

    if (!selectedTour) {
      return null;
    }

    return (
      <EquipmentValidationSection
        key={stop.id}
        stop={stop}
        equipmentItems={items}
        onEquipmentChange={(newItems) =>
          tourValidation.handleEquipmentChange(stop.id, selectedTour.id, newItems)
        }
        onValidationChange={(isValid) =>
          tourValidation.handleStopValidation(stop.id, selectedTour.id, isValid)
        }
        onToggleAllEquipments={(isChecked) =>
          tourValidation.handleToggleAllEquipments(stop.id, selectedTour.id, isChecked)
        }
      />
    );
  };

  const handleTourClick = (tour: TourWithDriver) => {
    setSelectedTour(tour);

    // Déterminer le type de contrôle selon le statut de la tournée
    const controlType = tour.status === TourStatus.Completed ? 'return' : 'departure';
    _setCurrentControlType(controlType);
  };

  // Obtenir le texte du bouton selon l'état et le type de contrôle
  const getValidationButtonText = () => {
    if (!selectedTour) {
      return 'Valider le pré-chargement';
    }

    const validation = tourValidations[selectedTour.id];
    const isReturnControl = selectedTour.status === TourStatus.Completed;

    if (isReturnControl) {
      if (validation?.isReturnControlValidated) {
        return 'Finaliser la tournée';
      }

      return 'Finaliser la tournée';
    } else {
      if (validation?.isCompletelyValidated) {
        return 'Revalider le pré-chargement';
      }

      return 'Valider le pré-chargement';
    }
  };

  const handleTourValidation = async () => {
    console.log('🔍 [handleTourValidation] Starting validation', {
      selectedTour: selectedTour?.id,
      tourNumber: selectedTour?.tourIdentifier.originalNumber,
      areAllStopsValidated: areAllStopsValidatedForSelectedTour,
    });

    if (!selectedTour) {
      console.log('❌ [handleTourValidation] No selected tour');
      presentToast({
        message: 'Aucune tournée sélectionnée.',
        duration: 3000,
        color: 'warning',
      });

      return;
    }

    // Vérifier que tous les arrêts sont validés
    if (!areAllStopsValidatedForSelectedTour) {
      console.log('❌ [handleTourValidation] Not all stops validated');
      presentToast({
        message: 'Veuillez valider tous les arrêts avant de continuer.',
        duration: 3000,
        color: 'warning',
      });

      return;
    }

    const isReturnControl = selectedTour.status === TourStatus.Completed;
    console.log('🔍 [handleTourValidation] Processing', { isReturnControl });

    try {
      if (isReturnControl) {
        // Pour les contrôles de retour, juste console.log pour l'instant

        // Reset du cache preload pour cette tournée
        await tourValidation.resetValidationsForTour(selectedTour.id);

        // Mettre à jour l'état de validation local pour les contrôles de retour
        const newValidationState: TourValidationState = {
          tourId: selectedTour.id,
          isCompletelyValidated: false,
          isPartiallyValidated: false,
          equipmentData: null,
          lastUpdated: Date.now(),
          controlType: 'return',
          isReturnControlValidated: true,
        };

        setTourValidations((prev) => ({
          ...prev,
          [selectedTour.id]: newValidationState,
        }));

        // Faire disparaître la carte de la liste
        setTours((prevTours) => prevTours.filter((tour) => tour.id !== selectedTour.id));

        presentToast({
          message: `✅ Contrôle de retour validé pour la tournée n°${selectedTour.tourIdentifier.originalNumber} (${selectedTour.driverName})`,
          duration: 4000,
          color: 'success',
        });
      } else {
        // Logique pour les contrôles de départ avec /preload
        console.log('🔍 [handleTourValidation] Processing preload for tour:', selectedTour.id);

        // Calculer les équipements totaux depuis les validations par arrêt
        let totalPalletCount = 0;
        let totalRollCount = 0;
        let totalPackageCount = 0;

        // Boucler sur tous les stops de la tournée
        console.log('🔍 [handleTourValidation] Processing stops:', selectedTour.stops?.length);

        for (const stop of selectedTour.stops || []) {
          const stopEquipments = tourValidation.stopEquipmentStates[stop.id] || [];
          console.log('🔍 [handleTourValidation] Stop equipment:', {
            stopId: stop.id,
            equipmentCount: stopEquipments.length,
            equipments: stopEquipments,
          });

          for (const item of stopEquipments) {
            if (item.isValidated && item.adjustedQuantity > 0) {
              console.log('🔍 [handleTourValidation] Processing validated item:', {
                type: item.type,
                quantity: item.adjustedQuantity,
                isValidated: item.isValidated,
              });

              switch (item.type) {
                case LogisticsEquipmentKind.PALLET:
                  totalPalletCount += item.adjustedQuantity;
                  break;
                case LogisticsEquipmentKind.ROLL:
                  totalRollCount += item.adjustedQuantity;
                  break;
                case LogisticsEquipmentKind.PACKAGE:
                  totalPackageCount += item.adjustedQuantity;
                  break;
              }
            }
          }
        }

        // Utiliser le hook preload avec le système d'événements
        console.log('🔍 [handleTourValidation] Calling preloadTour...');
        await preloadTour(selectedTour);

        // Reset du cache preload pour cette tournée après succès
        await tourValidation.resetValidationsForTour(selectedTour.id);

        // Mettre à jour l'état de validation local
        const newValidationState: TourValidationState = {
          tourId: selectedTour.id,
          isCompletelyValidated: true,
          isPartiallyValidated: true,
          equipmentData: null, // On n'utilise plus l'ancien système
          lastUpdated: Date.now(),
          controlType: 'departure',
          isReturnControlValidated: false,
        };

        setTourValidations((prev) => ({
          ...prev,
          [selectedTour.id]: newValidationState,
        }));

        // Faire disparaître la carte de la liste
        setTours((prevTours) => prevTours.filter((tour) => tour.id !== selectedTour.id));

        presentToast({
          message: `✅ Pré-chargement validé et sauvegardé pour la tournée n°${selectedTour.tourIdentifier.originalNumber} (${selectedTour.driverName})`,
          duration: 4000,
          color: 'success',
        });
      }

      // Fermer le modal
      setSelectedTour(null);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);

      presentToast({
        message: 'Erreur lors de la sauvegarde. Veuillez réessayer.',
        duration: 4000,
        color: 'danger',
      });
    }
  };

  const handleCompleteAllControls = () => {
    presentToast({
      message: 'Contrôle de pré-chargement des agrès terminé !',
      duration: 3000,
      color: 'success',
    });

    // Vider la liste après validation complète
    setTours([]);
  };

  const getTourStatusColor = (tour: ITourEntity) => {
    const validation = tourValidations[tour.id];
    const isReturnControl = tour.status === TourStatus.Completed;

    if (isReturnControl) {
      if (validation?.isReturnControlValidated) {
        return 'success';
      }

      return 'success'; // Couleur verte pour les contrôles de retour
    }

    if (validation?.isCompletelyValidated) {
      return 'success';
    } else if (validation?.isPartiallyValidated) {
      return 'warning'; // Orange pour "en cours"
    }

    return 'medium';
  };

  // Fonction pour vérifier si une tournée est déjà pré-chargée

  if (isLoading) {
    return <Loading />;
  }

  if (error) {
    return <Loading />;
  }

  return (
    <IonPage>
      <IonContent fullscreen>
        <div className="bg-gray-50 h-full mx-1 overflow-auto pt-4 sm:pt-6 md:pt-8 lg:pt-10 pb-10 sm:pb-12 md:pb-14 lg:pb-16">
          <div className="max-w-7xl mx-auto pt-2 sm:pt-4 md:pt-6 flex flex-col h-full px-2 sm:px-4 md:px-6 lg:px-8">
            {/* Header */}
            <div className="my-2 4 mb-6 sm:mb-8 md:mb-10 flex flex-col  justify-between gap-2 sm:gap-4">
              <div className="flex-1">
                <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-neutral-500 dark:text-neutral-300">
                  Bonjour {currentUser?.firstName || '--'}
                </p>
                <p className="text-sm sm:text-base text-gray-600 mt-1">
                  Contrôlez et validez les agrès de toutes les tournées
                </p>

                {/* Input de recherche */}
                <SearchInput
                  value={tourManagement.searchTerm}
                  onValueChange={tourManagement.setSearchTerm}
                  placeholder="Rechercher une tournée ou un chauffeur..."
                  className="mt-4"
                />
              </div>
            </div>

            {/* Segments pour les types de tournées */}
            <div className="mb-6">
              <IonSegment
                value={currentSegment}
                onIonChange={(e) => setCurrentSegment(e.detail.value as 'preload' | 'return')}
                className="bg-white rounded-lg shadow-sm h-16"
              >
                <IonSegmentButton
                  value="preload"
                  className="border-neutral-200 border-l-2 border-r-2"
                >
                  <IonLabel>Pré-chargement ({departureTours.length})</IonLabel>
                </IonSegmentButton>
                <IonSegmentButton value="return" className="border-neutral-200 border-r-2">
                  <IonLabel>Retour camion ({returnTours.length})</IonLabel>
                </IonSegmentButton>
              </IonSegment>
            </div>

            {/* Liste des tournées du segment actuel */}
            {filteredCurrentSegmentTours.length > 0 && (
              <div className="space-y-3">
                {filteredCurrentSegmentTours.map((tour) => (
                  <TourCard
                    key={tour.id}
                    tour={tour}
                    onClick={() => handleTourClick(tour)}
                    statusColor={getTourStatusColor(tour)}
                    showDeliveryDate={true}
                  />
                ))}
              </div>
            )}

            {/* Espace pour les boutons flottants */}
            <div className="pb-20"></div>

            {/* Message si aucune tournée pour le segment actuel */}
            {currentSegmentTours.length === 0 && (
              <div className="flex flex-col items-center justify-center h-64">
                <IonText className="text-gray-500 text-center">
                  <p className="text-lg font-semibold">
                    Aucune tournée {currentSegment === 'preload' ? 'à pré-charger' : 'finalisée'}
                  </p>
                </IonText>
              </div>
            )}

            {/* Message si aucun résultat de recherche */}
            {currentSegmentTours.length > 0 &&
              filteredCurrentSegmentTours.length === 0 &&
              tourManagement.searchTerm.trim() && (
                <div className="flex flex-col items-center justify-center h-64">
                  <IonText className="text-gray-500 text-center">
                    <p className="text-lg font-semibold">Aucune tournée trouvée</p>
                    <p className="text-sm">
                      Aucune tournée ne correspond à "{tourManagement.searchTerm}"
                    </p>
                    <IonButton
                      fill="clear"
                      size="small"
                      onClick={() => tourManagement.setSearchTerm('')}
                      className="mt-2 text-blue-600"
                    >
                      Effacer la recherche
                    </IonButton>
                  </IonText>
                </div>
              )}
          </div>
        </div>

        {/* Bouton de validation globale flottant */}
        {allToursValidated && availableTours.length > 0 && (
          <div className="fixed bottom-0 bg-white py-6 left-0 right-0 z-10 p-4">
            <div className="max-w-7xl mx-auto">
              <InlineButtons
                buttons={[
                  {
                    label: 'Terminer le contrôle de pré-chargement des agrès',
                    onClick: handleCompleteAllControls,
                    classNames: {
                      label: 'text-lg font-semibold',

                      button: 'primary-button py-6 px-6',
                    },
                  },
                ]}
              />
            </div>
          </div>
        )}

        {/* Modal de validation */}
        <IonModal isOpen={!!selectedTour} onDidDismiss={() => setSelectedTour(null)}>
          <div className="shadow-none border-none">
            <div className="flex justify-between items-center px-4 py-4 border-b border-gray-200 bg-gray-50 primary-color">
              <div className="flex flex-col gap-2">
                <div className="text-xl font-bold">
                  {selectedTour?.status === TourStatus.Completed
                    ? 'Tournée finalisée'
                    : 'Pré-chargement des agrès'}{' '}
                  n°{selectedTour?.tourIdentifier.originalNumber}
                </div>
                <div className="text-sm font-medium text-gray-900/80">
                  par{' '}
                  <span className="font-bold primary-color underline">
                    {selectedTour?.driverName}
                  </span>{' '}
                  le {formatDate(selectedTour?.deliveryDate)}
                </div>
              </div>
              <div className="cursor-pointer" onClick={() => setSelectedTour(null)}>
                <X className="h-6 w-6" />
              </div>
            </div>
          </div>
          <IonContent className="ion-padding">
            {selectedTour && (
              <div className="pt-2 pb-20">
                {/* Liste des cartes de livraisons */}
                <div className="space-y-4 mb-8">
                  {selectedTour.stops && selectedTour.stops.length > 0 ? (
                    selectedTour.stops.map(renderStopCard)
                  ) : (
                    <div className="text-center py-8">
                      <IonText>
                        <p className="text-lg text-neutral-500">
                          Aucune livraison dans cette tournée
                        </p>
                      </IonText>
                    </div>
                  )}
                </div>
              </div>
            )}
          </IonContent>

          {/* Bouton de validation flottant dans la modal */}
          {selectedTour && (
            <div className="fixed bottom-0 bg-white py-6 left-0 right-0 z-10 p-4">
              <div className="max-w-7xl mx-4">
                <InlineButtons
                  buttons={[
                    {
                      label: getValidationButtonText(),
                      onClick: handleTourValidation,
                      disabled: !areAllStopsValidatedForSelectedTour,
                      classNames: {
                        label: 'text-lg font-semibold',

                        button: areAllStopsValidatedForSelectedTour
                          ? 'primary-button py-6 px-6'
                          : 'primary-button-outline opacity-50 py-6 px-6',
                      },
                    },
                  ]}
                />
              </div>
            </div>
          )}
        </IonModal>
      </IonContent>
    </IonPage>
  );
};

export default ReceptionistDashboard;
